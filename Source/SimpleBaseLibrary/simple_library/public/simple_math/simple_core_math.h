// Copyright (C) RenZhai.2022.All Rights Reserved.
#pragma once
#include "math_libray.hpp"
#include "simple_math.h"
#include "math_utils.h"

//����
#include "transformation/vector/vector_2d.h"
#include "transformation/vector/vector_3d.h"
#include "transformation/vector/vector_4d.h"
#include "transformation/vector/vector_2id.h"
#include "transformation/vector/vector_3id.h"
#include "transformation/vector/vector_4id.h"
#include "transformation/vector/vector_color.h"

//ŷ����
#include "transformation/rotator/rotator.h"

//����
#include "transformation/matrix/matrix_2x2.h"
#include "transformation/matrix/matrix_3x3.h"
#include "transformation/matrix/matrix_4x4.h"
#include "transformation/matrix/matrix_4x3.h"

//ͼԪ
#include "transformation/primitives/triangle.h"
#include "transformation/primitives/line.h"