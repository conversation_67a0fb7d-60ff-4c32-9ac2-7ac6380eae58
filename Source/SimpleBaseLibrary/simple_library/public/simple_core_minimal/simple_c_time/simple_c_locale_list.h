// Copyright (C) RenZhai.2022.All Rights Reserved.
#pragma once
#include <locale.h>

	//������/��������
	const char af[] = "af";					//�ϷǺ�����
	const char af_ZA[] = "af-ZA";			//�ϷǺ�����Ϸǣ�
	const char sq[] = "sq";					//������������
	const char sq_AL[] = "sq-AL";			//��������������������ǣ�
	const char ar[] = "ar";					//��������
	const char ar_DZ[] = "ar-DZ";			//����������������ǣ�
	const char ar_BH[] = "ar-BH";			//����������֣�
	const char ar_EG[] = "ar-EG";			//�������������
	const char ar_IQ[] = "ar-IQ";			//������������ˣ�
	const char ar_JO[] = "ar-JO";			//�������Լ����
	const char ar_KW[] = "ar-KW";			//������������أ�
	const char ar_LB[] = "ar-LB";			//�����������ۣ�
	const char ar_LY[] = "ar-LY";			//������������ǣ�
	const char ar_MA[] = "ar-MA";			//�������Ħ��磩
	const char ar_OM[] = "ar-OM";			//�������������
	const char ar_QA[] = "ar-QA";			//���������������
	const char ar_SA[] = "ar-SA";			//�������ɳ�ذ�������
	const char ar_SY[] = "ar-SY";			//������������ǣ�
	const char ar_TN[] = "ar-TN";			//�������ͻ��˹��
	const char ar_AE[] = "ar-AE";			//���������������
	const char ar_YE[] = "ar-YE";			//�������Ҳ�ţ�
	const char hy[] = "hy";					//����������
	const char hy_AM[] = "hy-AM";			//����������������ǣ�
	const char az[] = "az";					//��������
	const char az_Cyrl_AZ[] = "az-Cyrl-AZ";	//������������ݽ���������
	const char az_Latn_AZ[] = "az-Latn-AZ";	//������������ݽ��������
	const char eu[] = "eu";					//��˹����
	const char eu_ES[] = "eu-ES";			//��˹�����˹�˵�����
	const char be[] = "be";					//�׶���˹��
	const char be_BY[] = "be-BY";			//�׶���˹��׶���˹��
	const char bg[] = "bg";					//����������
	const char bg_BG[] = "bg-BG";			//����������������ǣ�
	const char ca[] = "ca";					//��̩��������
	const char ca_ES[] = "ca-ES";			//��̩���������̩�����ǵ�����
	const char zh_HK[] = "zh-HK";			//���ģ�����ر����������й���
	const char zh_MO[] = "zh-MO";			//���ģ������ر���������
	const char zh_CN[] = "zh-CN";			//���ģ��й���
	const char zh_Hans[] = "zh-Hans";		//���ģ����壩
	const char zh_SG[] = "zh-SG";			//���ģ��¼��£�
	const char zh_TW[] = "zh-TW";			//���ģ�̨�壩
	const char zh_Hant[] = "zh-Hant";		//���ģ����壩
	const char hr[] = "hr";					//���޵�����
	const char hr_HR[] = "hr-HR";			//���޵�������޵��ǣ�
	const char cs[] = "cs";					//�ݿ���
	const char cs_CZ[] = "cs-CZ";			//�ݿ���ݿ˹��͹���
	const char da[] = "da";					//������
	const char da_DK[] = "da-DK";			//���������
	const char dv[] = "dv";					//��άϣ��
	const char dv_MV[] = "dv-MV";			//��άϣ��������
	const char nl[] = "nl";					//������
	const char nl_BE[] = "nl-BE";			//���������ʱ��
	const char nl_NL[] = "nl-NL";			//�����������
	const char en[] = "en";					//Ӣ��
	const char en_AU[] = "en-AU";			//Ӣ��Ĵ����ǣ�
	const char en_BZ[] = "en-BZ";			//Ӣ������ȣ�
	const char en_CA[] = "en-CA";			//Ӣ����ô�
	const char en_029[] = "en-029";			//Ӣ����ձȣ�
	const char en_IE[] = "en-IE";			//Ӣ���������
	const char en_JM[] = "en-JM";			//Ӣ�����ӣ�
	const char en_NZ[] = "en-NZ";			//Ӣ���������
	const char en_PH[] = "en-PH";			//Ӣ����ɱ���
	const char en_ZA[] = "en-ZA";			//Ӣ��Ϸǣ�
	const char en_TT[] = "en-TT";			//Ӣ��������Ͷ�͸磩
	const char en_GB[] = "en-GB";			//Ӣ�Ӣ����
	const char en_US[] = "en-US";			//Ӣ�������
	const char en_ZW[] = "en-ZW";			//Ӣ���Ͳ�Τ��
	const char et[] = "et";					//��ɳ������
	const char et_EE[] = "et-EE";			//��ɳ�������ɳ���ǣ�
	const char fo[] = "fo";					//������
	const char fo_FO[] = "fo-FO";			//���������Ⱥ����
	const char fa[] = "fa";					//��˹��
	const char fa_IR[] = "fa-IR";			//��˹����ʣ�
	const char fi[] = "fi";					//������
	const char fi_FI[] = "fi-FI";			//�����������
	const char fr[] = "fr";					//����
	const char fr_BE[] = "fr-BE";			//�������ʱ��
	const char fr_CA[] = "fr-CA";			//������ô�
	const char fr_FR[] = "fr-FR";			//���������
	const char fr_LU[] = "fr-LU";			//���¬ɭ����
	const char fr_MC[] = "fr-MC";			//���Ħ�ɸ磩
	const char fr_CH[] = "fr-CH";			//�����ʿ��
	const char gl[] = "gl";					//����������
	const char gl_ES[] = "gl-ES";			//�����������������
	const char ka[] = "ka";					//��³������
	const char ka_GE[] = "ka-GE";			//��³�������³���ǣ�
	const char de[] = "de";					//����
	const char de_AT[] = "de-AT";			//����µ�����
	const char de_DE[] = "de-DE";			//����¹���
	const char de_LI[] = "de-LI";			//�����֧��ʿ�ǣ�
	const char de_LU[] = "de-LU";			//���¬ɭ����
	const char de_CH[] = "de-CH";			//�����ʿ��
	const char el[] = "el";					//ϣ����
	const char el_GR[] = "el-GR";			//ϣ���ϣ����
	const char gu[] = "gu";					//�ż�������
	const char gu_IN[] = "gu-IN";			//�ż������ӡ�ȣ�
	const char he[] = "he";					//ϣ������
	const char he_IL[] = "he-IL";			//ϣ�������ɫ�У�
	const char hi[] = "hi";					//ӡ����
	const char hi_IN[] = "hi-IN";			//ӡ���ӡ�ȣ�
	const char hu[] = "hu";					//��������
	const char hu_HU[] = "hu-HU";			//���������������
	const char is[] = "is";					//������
	const char is_IS[] = "is-IS";			//�����������
	const char id[] = "id";					//ӡ����������
	const char id_ID[] = "id-ID";			//ӡ���������ӡ�������ǣ�
	const char it[] = "it";					//�������
	const char it_IT[] = "it-IT";			//�������������
	const char it_CH[] = "it-CH";			//��������ʿ��
	const char ja[] = "ja";					//����
	const char ja_JP[] = "ja-JP";			//����ձ���
	const char kn[] = "kn";					//���ɴ���
	const char kn_IN[] = "kn-IN";			//���ɴ��ӡ�ȣ�
	const char kk[] = "kk";					//��������
	const char kk_KZ[] = "kk-KZ";			//�������������˹̹��
	const char kok[] = "kok";				//������
	const char kok_IN[] = "kok-IN";			//�����ӡ�ȣ�
	const char ko[] = "ko";					//������
	const char ko_KR[] = "ko-KR";			//�����������
	const char ky[] = "ky";					//������˹��
	const char ky_KG[] = "ky-KG";			//������˹�������˹̹��
	const char lv[] = "lv";					//����ά����
	const char lv_LV[] = "lv-LV";			//����ά�������ά�ǣ�
	const char lt[] = "lt";					//��������
	const char lt_LT[] = "lt-LT";			//�������������
	const char mk[] = "mk";					//�������
	const char mk_MK[] = "mk-MK";			//����������٣�FYROM��
	const char ms[] = "ms";					//������
	const char ms_BN[] = "ms-BN";			//�����������³������
	const char ms_MY[] = "ms-MY";			//������������ǣ�
	const char mr[] = "mr";					//��������
	const char mr_IN[] = "mr-IN";			//�������ӡ�ȣ�
	const char mn[] = "mn";					//�ɹ���
	const char mn_MN[] = "mn-MN";			//�ɹ���ɹţ�
	const char no[] = "no";					//Ų����
	const char nb_NO[] = "nb-NO";			//Ų�������÷����Ų����
	const char nn_NO[] = "nn-NO";			//Ų�����ŵ˹�ˣ�Ų����
	const char pl[] = "pl";					//������
	const char pl_PL[] = "pl-PL";			//�����������
	const char pt[] = "pt";					//��������
	const char pt_BR[] = "pt-BR";			//�������������
	const char pt_PT[] = "pt-PT";			//���������������
	const char pa[] = "pa";					//��������
	const char pa_IN[] = "pa-IN";			//�������ӡ�ȣ�
	const char ro[] = "ro";					//����������
	const char ro_RO[] = "ro-RO";			//����������������ǣ�
	const char ru[] = "ru";					//����
	const char ru_RU[] = "ru-RU";			//�������˹��
	const char sa[] = "sa";					//����
	const char sa_IN[] = "sa-IN";			//���ӡ�ȣ�
	const char sr_Cyrl_CS[] = "sr-Cyrl-CS";	//����ά�������ά�ǣ�������
	const char sr_Latn_CS[] = "sr-Latn-CS";	//����ά�������ά�ǣ������
	const char sk[] = "sk";					//˹�工����
	const char sk_SK[] = "sk-SK";			//˹�工���˹�工�ˣ�
	const char sl[] = "sl";					//˹����������
	const char sl_SI[] = "sl-SI";			//˹���������˹�������ǣ�
	const char es[] = "es";					//��������
	const char es_AR[] = "es-AR";			//�����������͢��
	const char es_BO[] = "es-BO";			//�����������ά�ǣ�
	const char es_CL[] = "es-CL";			//�������������
	const char es_CO[] = "es-CO";			//����������ױ��ǣ�
	const char es_CR[] = "es-CR";			//���������˹����ӣ�
	const char es_DO[] = "es-DO";			//�������������ӹ��͹���
	const char es_EC[] = "es-EC";			//���������϶����
	const char es_SV[] = "es-SV";			//������������߶ࣩ
	const char es_GT[] = "es-GT";			//�������Σ��������
	const char es_HN[] = "es-HN";			//��������鶼��˹��
	const char es_MX[] = "es-MX";			//�������ī���磩
	const char es_NI[] = "es-NI";			//�������������ϣ�
	const char es_PA[] = "es-PA";			//�������������
	const char es_PY[] = "es-PY";			//������������磩
	const char es_PE[] = "es-PE";			//����������³��
	const char es_PR[] = "es-PR";			//����������������
	const char es_ES[] = "es-ES";			//���������������
	const char es_UY[] = "es-UY";			//������������磩
	const char es_VE[] = "es-VE";			//�������ί��������
	const char sw[] = "sw";					//˹��ϣ����
	const char sw_KE[] = "sw-KE";			//˹��ϣ��������ǣ�
	const char sv[] = "sv";					//�����
	const char sv_FI[] = "sv-FI";			//����������
	const char sv_SE[] = "sv-SE";			//������䣩
	const char syr[] = "syr";				//��������
	const char syr_SY[] = "syr-SY";			//������������ǣ�
	const char ta[] = "ta";					//̩�׶���
	const char ta_IN[] = "ta-IN";			//̩�׶��ӡ�ȣ�
	const char tt[] = "tt";					//������
	const char tt_RU[] = "tt-RU";			//���������˹��
	const char te[] = "te";					//̩¬����
	const char te_IN[] = "te-IN";			//̩¬���ӡ�ȣ�
	const char th[] = "th";					//̩��
	const char th_TH[] = "th-TH";			//̩�̩����
	const char tr[] = "tr";					//��������
	const char tr_TR[] = "tr-TR";			//������������䣩
	const char uk[] = "uk";					//�ڿ�����
	const char uk_UA[] = "uk-UA";			//�ڿ�����ڿ�����
	const char ur[] = "ur";					//�ڶ�����
	const char ur_PK[] = "ur-PK";			//�ڶ�����ͻ�˹̹��
	const char uz[] = "uz";					//���ȱ����
	const char uz_Cyrl_UZ[] = "uz-Cyrl-UZ";	//���ȱ������ȱ��˹̹��������
	const char uz_Latn_UZ[] = "uz-Latn-UZ";	//���ȱ������ȱ��˹̹�������
	const char vi[] = "vi";					//Խ����
	const char vi_VN[] = "vi-VN";			//Խ���Խ�ϣ�

	//���뷽ʽ
	const char UTF8[] = "UTF-8";			//UTF-8

	
