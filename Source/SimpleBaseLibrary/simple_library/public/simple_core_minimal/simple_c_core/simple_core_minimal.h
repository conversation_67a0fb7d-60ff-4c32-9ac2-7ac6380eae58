#pragma once
//Copyright (C) RenZhai.2019.All Rights Reserved.
//���� ��լ
//��c�����ϸԴ�뽲����AboutCG ���ְ�������Ӳ��c���ԡ���Ƶ�������棺
//https://www.aboutcg.org/courseDetails/902/introduce
//ϣ��ѧϰ�������漼�� ������UE4��Ϸ, ���Է���������ַ��
//https://zhuanlan.zhihu.com/p/60117613
//
//bibi���Կ���������լϵ�н̳̽��ܣ�
//https://space.bilibili.com/29544409
//
//���˲�����վ
//http://renzhai.net
//https://www.cnblogs.com/Shirlies/p/5137548.html
//���ڱ��װ�������ϸ���� :
//���ְ汾(��ϸ)��
//https://zhuanlan.zhihu.com/p/144558934
//��Ƶ�汾��
//https://www.bilibili.com/video/BV1x5411s7s3
//#ifndef WIN32_LEAN_AND_MEAN 
//#define WIN32_LEAN_AND_MEAN 
//#endif
#ifndef _CRT_SECURE_NO_WARNINGS
#define _CRT_SECURE_NO_WARNINGS
#endif // !1
#ifndef _WINSOCK_DEPRECATED_NO_WARNINGS
#define _WINSOCK_DEPRECATED_NO_WARNINGS
#endif // !1

#include <WinSock2.h>
#pragma comment(lib,"ws2_32.lib")

#include <windows.h>
#include <vcruntime.h>
#include <assert.h>
#include <stdio.h>
#include <corecrt_wstdio.h>
#include <stdbool.h>
#include <corecrt_wstdio.h>
#include <stdarg.h>
#include <combaseapi.h>
#include <errno.h>
#include <stdlib.h>
#include <string.h>
#include <io.h>
#include <direct.h>
#include <time.h>
#include <wchar.h>
#include <process.h>
#include <shellapi.h>
#include <corecrt_wstring.h>

//#include <array>

#define SIMPLE_C_BUFF_SIZE 8196 //�����С�͸����