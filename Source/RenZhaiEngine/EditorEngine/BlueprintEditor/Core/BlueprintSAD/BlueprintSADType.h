#pragma once

#include "../../../../Engine/EngineMinimal.h"

enum EBlueprintPinType;
enum EUnrealEnginePinType;
enum EBlueprintNodeType;

class CClassObject;

enum EAdvancedPinDisplay
{
	Hidden,
	Shown,
};

enum EEnabledState
{
	DevelopmentOnly,
};

struct FEventReference
{
	string MemberParent;///Script/CoreUObject.Class
	string MemberName;
};

///Script/BlueprintGraph.K2Node_Event Name="K2Node_Event_0"
struct FObjectClassDesc
{
	string ClassScript;// /Script/BlueprintGraph.K2Node_Event
	string Name;//K2Node_Event_0
};

enum FPinContainerType
{
	None,
};

struct FPinType
{
	FPinType();

	string PinCategory;//"delegate"
	string PinSubCategory;
	string PinSubCategoryObject;
	FEventReference PinSubCategoryMemberReference;
	string PinValueType;
	FPinContainerType PinContainerType;

	bool bIsReference;
	bool bIsConst;
	bool bIsWeakPointer;
	bool bIsUObjectWrapper;
	bool bSerializeAsSinglePrecisionFloat;
};

struct FLinked :public std::enable_shared_from_this<FLinked>
{
	string LinkName;
	simple_c_guid GUID;
};

struct FCustomProperties
{
	FCustomProperties();

	simple_c_guid PinId;
	string PinName;
	EBlueprintPinType Direction;
	FPinType PinType;
	std::shared_ptr<FLinked> LinkedTo;
	simple_c_guid PersistentGuid;

	string DefaultValue;
	string AutogeneratedDefaultValue;
	string PinToolTip;
	string PinFriendlyName;
	string DefaultObject;

	bool bHidden;
	bool bNotConnectable;
	bool bDefaultValueIsReadOnly;
	bool bDefaultValueIsIgnored;
	bool bAdvancedView;
	bool bOrphanedPin;
};

struct FBlueprintNodeData
{
	FBlueprintNodeData();

	EBlueprintNodeType NodeType;
	FObjectClassDesc ObjectClass;// /Script/BlueprintGraph.K2Node_Event Name="K2Node_Event_0"
	FEventReference Reference;
	fvector_2d NodePos;//
	EAdvancedPinDisplay AdvancedPinDisplay;
	EEnabledState EnabledState;
	bool bOverrideFunction;//�Ƿ�Ϊ��д����
	bool bCommentBubblePinned;
	bool bIsPureFunc;

	simple_c_guid NodeGuid;

	vector<FCustomProperties> CustomProperties;
};

typedef vector<FBlueprintNodeData> FBlueprintDatas;

namespace BlueprintSerializeType
{
	EBlueprintPinType UnrealEnginePinTypeToOwnerEnginePinType(EUnrealEnginePinType Pin);
	EUnrealEnginePinType OwnerEnginePinTypeToUnrealEnginePinType(EBlueprintPinType Pin);
}