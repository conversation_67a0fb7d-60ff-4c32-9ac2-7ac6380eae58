<?xml version="1.0" encoding="utf-8" ?>
<Project DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
    <ItemGroup Label="ProjectConfigurations">
        <ProjectConfiguration Include="Debug|Win32">
            <Configuration>Debug</Configuration>
            <Platform>Win32</Platform>
        </ProjectConfiguration>
        <ProjectConfiguration Include="Release|Win32">
            <Configuration>Release</Configuration>
            <Platform>Win32</Platform>
        </ProjectConfiguration>
        <ProjectConfiguration Include="Debug|x64">
            <Configuration>Debug</Configuration>
            <Platform>x64</Platform>
        </ProjectConfiguration>
        <ProjectConfiguration Include="Release|x64">
            <Configuration>Release</Configuration>
            <Platform>x64</Platform>
        </ProjectConfiguration>
    </ItemGroup>
    <ItemGroup>
        <ClCompile Include="Common\EngineVariableTable.cpp" />
        <ClCompile Include="Common\OperationHandleSelectManage.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\BlueprintCompile\BlueprintCompiledStatement.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\BlueprintCompile\BlueprintCompilerContext.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\BlueprintCompile\BlueprintFunctionContext.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\BlueprintCompile\CompilerBytecodeBackend.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\BlueprintCompile\ScriptBuilder.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\BlueprintConfigInfo.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\BlueprintFunctionLibrary.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\Core\BlueprintConnection\BlueprintConnectionExtend\BlueprintBezierCubicConnection.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\Core\BlueprintConnection\BlueprintConnectionExtend\BlueprintBezierQuadraticConnection.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\Core\BlueprintConnection\BlueprintConnectionExtend\BlueprintLineConnection.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\Core\BlueprintConnection\BlueprintConnectionManage.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\Core\BlueprintConnection\BlueprintDrawConnection.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\Core\BlueprintConnection\Interface\BlueprintConnection.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\Core\BlueprintEditor\BlueprintEditor.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\Core\BlueprintGraphic\GraphicBlueprintEditor.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\Core\BlueprintMenu\BlueprintNodeMenuEditor.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\Core\BlueprintNode\BlueprintNode.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\Core\BlueprintNode\BlueprintPin.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\Core\BlueprintNode\Node.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\Core\BlueprintSAD\BlueprintDeserialization\BlueprintDeserialization.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\Core\BlueprintSAD\BlueprintSADType.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\Core\BlueprintSAD\BlueprintSerialize\BlueprintSerialize.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\Core\BlueprintToolbarEidtor\BlueprintToolbarEditor.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\Core\BlueprintToolButton\BlueprintToolButtonEditor.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Details\ObjectDetailsEditor.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Graphic\ObjectGraphicBlueprintEditor.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Menu\ObjectBlueprintNodeMenuEditor.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Node\BlueprintNode\EventBlueprintNode.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Node\BlueprintNode\FunctionBlueprintNode.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Node\BlueprintNode\ObjectBlueprintNode.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Node\BlueprintNode\PureFunctionBlueprintNode.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Node\BlueprintPin\BoolBlueprintPin.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Node\BlueprintPin\FloatBlueprintPin.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Node\BlueprintPin\IntBlueprintPin.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Node\BlueprintPin\ObjectBlueprintPin.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Node\BlueprintPin\StringBlueprintPin.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Node\BlueprintPin\VectorBlueprintPin.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\ObjectBlueprintEditor.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Toolbar\ObjectBlueprintToolbarEditor.cpp" />
        <ClCompile Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\ToolButton\ObjectBlueprintToolButtonEditor.cpp" />
        <ClCompile Include="EditorEngine\BrowseEditor\BrowseEditor.cpp" />
        <ClCompile Include="EditorEngine\BrowseEditor\Core\AssetEditorCommon.cpp" />
        <ClCompile Include="EditorEngine\BrowseEditor\Core\AssetFileDirectory.cpp" />
        <ClCompile Include="EditorEngine\BrowseEditor\Editor\AssetEditor.cpp" />
        <ClCompile Include="EditorEngine\BrowseEditor\Editor\Element\BrowseAssetIcon.cpp" />
        <ClCompile Include="EditorEngine\BrowseEditor\Editor\FileEditor.cpp" />
        <ClCompile Include="EditorEngine\BrowseEditor\Editor\Menu\AssetMenuEditor.cpp" />
        <ClCompile Include="EditorEngine\BrowseEditor\Editor\Menu\FolderMenuEditor.cpp" />
        <ClCompile Include="EditorEngine\BrowseEditor\Editor\Menu\ObjectAssetMenuEditor.cpp" />
        <ClCompile Include="EditorEngine\Core\EditorBase.cpp" />
        <ClCompile Include="EditorEngine\Core\EditorCommon.cpp" />
        <ClCompile Include="EditorEngine\DetailsEditor\Core\ClassDetailsMapping.cpp" />
        <ClCompile Include="EditorEngine\DetailsEditor\Core\ConstructDetailsWidget.cpp" />
        <ClCompile Include="EditorEngine\DetailsEditor\Core\DetailsMapping.cpp" />
        <ClCompile Include="EditorEngine\DetailsEditor\Core\PropertyDetailsMapping.cpp" />
        <ClCompile Include="EditorEngine\DetailsEditor\Core\RegisterDetailsMapping.cpp" />
        <ClCompile Include="EditorEngine\DetailsEditor\DetailsEditor.cpp" />
        <ClCompile Include="EditorEngine\DetailsEditor\Mappings\ActorObjectDetailsMapping.cpp" />
        <ClCompile Include="EditorEngine\DetailsEditor\Mappings\ArrayDetailsMapping.cpp" />
        <ClCompile Include="EditorEngine\DetailsEditor\Mappings\BoolDetailsMapping.cpp" />
        <ClCompile Include="EditorEngine\DetailsEditor\Mappings\ContainerDetailsMapping.cpp" />
        <ClCompile Include="EditorEngine\DetailsEditor\Mappings\FloatDetailsMapping.cpp" />
        <ClCompile Include="EditorEngine\DetailsEditor\Mappings\IntDetailsMapping.cpp" />
        <ClCompile Include="EditorEngine\DetailsEditor\Mappings\MapDetailsMapping.cpp" />
        <ClCompile Include="EditorEngine\DetailsEditor\Mappings\RotatorDetailsMapping.cpp" />
        <ClCompile Include="EditorEngine\DetailsEditor\Mappings\StringDetailsMapping.cpp" />
        <ClCompile Include="EditorEngine\DetailsEditor\Mappings\Vector3DDetailsMapping.cpp" />
        <ClCompile Include="EditorEngine\DetailsEditor\Mappings\XMFLOAT3DetailsMapping.cpp" />
        <ClCompile Include="EditorEngine\DetailsEditor\OutsideDetailsEditor.cpp" />
        <ClCompile Include="EditorEngine\EditorEngine.cpp" />
        <ClCompile Include="EditorEngine\LogEditor\LogEditor.cpp" />
        <ClCompile Include="EditorEngine\LogEditor\LogObject\LogSystem.cpp" />
        <ClCompile Include="EditorEngine\OutLineEditor\OutLineEditor.cpp" />
        <ClCompile Include="EditorEngine\SelectEditor\OperationHandle\Core\OperationHandleBase.cpp" />
        <ClCompile Include="EditorEngine\SelectEditor\OperationHandle\MoveArrow.cpp" />
        <ClCompile Include="EditorEngine\SelectEditor\OperationHandle\RotatorArrow.cpp" />
        <ClCompile Include="EditorEngine\SelectEditor\OperationHandle\ScalingArrow.cpp" />
        <ClCompile Include="EditorEngine\ToolbarEditor\ToolbarEditor.cpp" />
        <ClCompile Include="Engine\Actor\Core\ActorObject.cpp" />
        <ClCompile Include="Engine\Actor\Light\Core\Light.cpp" />
        <ClCompile Include="Engine\Actor\Light\Core\RangeLight.cpp" />
        <ClCompile Include="Engine\Actor\Light\ParallelLight.cpp" />
        <ClCompile Include="Engine\Actor\Light\PointLight.cpp" />
        <ClCompile Include="Engine\Actor\Light\SpotLight.cpp" />
        <ClCompile Include="Engine\Actor\Sky\Fog.cpp" />
        <ClCompile Include="Engine\Actor\Sky\Sky.cpp" />
        <ClCompile Include="Engine\Animation\Core\AnimationSequence.cpp" />
        <ClCompile Include="Engine\Animation\Core\BoneAnimation.cpp" />
        <ClCompile Include="Engine\Animation\Core\SkinnedAnimationData.cpp" />
        <ClCompile Include="Engine\Animation\Skeleton\Bone.cpp" />
        <ClCompile Include="Engine\Animation\Skeleton\Skeleton.cpp" />
        <ClCompile Include="Engine\Animation\SkinnedAnimationInstance.cpp" />
        <ClCompile Include="Engine\Collision\CollisionSceneQuery.cpp" />
        <ClCompile Include="Engine\Component\Input\Input.cpp" />
        <ClCompile Include="Engine\Component\Input\InputType.cpp" />
        <ClCompile Include="Engine\Component\InputComponent.cpp" />
        <ClCompile Include="Engine\Component\Light\Core\LightComponent.cpp" />
        <ClCompile Include="Engine\Component\Light\Core\LightConstantBuffer.cpp" />
        <ClCompile Include="Engine\Component\Light\Core\LightType.cpp" />
        <ClCompile Include="Engine\Component\Light\Core\RangeLightComponent.cpp" />
        <ClCompile Include="Engine\Component\Light\ParallelLightComponent.cpp" />
        <ClCompile Include="Engine\Component\Light\PointLightComponent.cpp" />
        <ClCompile Include="Engine\Component\Light\SpotLightComponent.cpp" />
        <ClCompile Include="Engine\Component\Mesh\BoxMeshComponent.cpp" />
        <ClCompile Include="Engine\Component\Mesh\ConeMeshComponent.cpp" />
        <ClCompile Include="Engine\Component\Mesh\Core\MeshAssist.cpp" />
        <ClCompile Include="Engine\Component\Mesh\Core\MeshComponent.cpp" />
        <ClCompile Include="Engine\Component\Mesh\Core\MeshComponentType.cpp" />
        <ClCompile Include="Engine\Component\Mesh\Core\ShellMeshComponent.cpp" />
        <ClCompile Include="Engine\Component\Mesh\CustomMeshComponent.cpp" />
        <ClCompile Include="Engine\Component\Mesh\CylinderMeshComponent.cpp" />
        <ClCompile Include="Engine\Component\Mesh\PipeMeshComponent.cpp" />
        <ClCompile Include="Engine\Component\Mesh\PlaneMeshComponent.cpp" />
        <ClCompile Include="Engine\Component\Mesh\PyramidMeshComponent.cpp" />
        <ClCompile Include="Engine\Component\Mesh\SkinnedMeshComponent.cpp" />
        <ClCompile Include="Engine\Component\Mesh\SphereMeshComponent.cpp" />
        <ClCompile Include="Engine\Component\Mesh\TorusMeshComponent.cpp" />
        <ClCompile Include="Engine\Component\Sky\FogComponent.cpp" />
        <ClCompile Include="Engine\Component\Sky\SkyConstantBuffer.cpp" />
        <ClCompile Include="Engine\Component\TimelineComponent.cpp" />
        <ClCompile Include="Engine\Component\TransformationComponent.cpp" />
        <ClCompile Include="Engine\Config\EngineRenderConfig.cpp" />
        <ClCompile Include="Engine\Core\Camera.cpp" />
        <ClCompile Include="Engine\Core\Construction\ActorMeshConstruction.cpp" />
        <ClCompile Include="Engine\Core\Construction\MeshConstruction.cpp" />
        <ClCompile Include="Engine\Core\Engine.cpp" />
        <ClCompile Include="Engine\Core\Viewport\ClientViewport.cpp" />
        <ClCompile Include="Engine\Core\Viewport\Viewport.cpp" />
        <ClCompile Include="Engine\Core\Viewport\ViewportInfo.cpp" />
        <ClCompile Include="Engine\Core\Viewport\ViewportTransformation.cpp" />
        <ClCompile Include="Engine\Core\WinMainCommandParameters.cpp" />
        <ClCompile Include="Engine\Core\World.cpp" />
        <ClCompile Include="Engine\EngineFactory.cpp" />
        <ClCompile Include="Engine\EngineType.cpp" />
        <ClCompile Include="Engine\Interface\DirectXDeviceInterfece.cpp" />
        <ClCompile Include="Engine\Interface\OpenGLDeviceInterfece.cpp" />
        <ClCompile Include="Engine\Library\Core\BlueprinFunctiontLibrary.cpp" />
        <ClCompile Include="Engine\Library\EngineMisLibrary.cpp" />
        <ClCompile Include="Engine\Library\MathLibrary.cpp" />
        <ClCompile Include="Engine\Library\RaycastSystemLibrary.cpp" />
        <ClCompile Include="Engine\Manage\LightManage.cpp" />
        <ClCompile Include="Engine\Manage\ViewportDataManage.cpp" />
        <ClCompile Include="Engine\Math\EngineMath.cpp" />
        <ClCompile Include="Engine\Mesh\BoxMesh.cpp" />
        <ClCompile Include="Engine\Mesh\ConeMesh.cpp" />
        <ClCompile Include="Engine\Mesh\Core\Material\Material.cpp" />
        <ClCompile Include="Engine\Mesh\Core\Material\MaterialConstantBuffer.cpp" />
        <ClCompile Include="Engine\Mesh\Core\Material\MaterialType.cpp" />
        <ClCompile Include="Engine\Mesh\Core\Mesh.cpp" />
        <ClCompile Include="Engine\Mesh\Core\MeshManage.cpp" />
        <ClCompile Include="Engine\Mesh\Core\MeshType.cpp" />
        <ClCompile Include="Engine\Mesh\Core\ObjectTransformation.cpp" />
        <ClCompile Include="Engine\Mesh\Core\SkinnedTransformation.cpp" />
        <ClCompile Include="Engine\Mesh\CustomMesh.cpp" />
        <ClCompile Include="Engine\Mesh\CylinderMesh.cpp" />
        <ClCompile Include="Engine\Mesh\PipeMesh.cpp" />
        <ClCompile Include="Engine\Mesh\PlaneMesh.cpp" />
        <ClCompile Include="Engine\Mesh\PyramidMesh.cpp" />
        <ClCompile Include="Engine\Mesh\SkinnedMesh.cpp" />
        <ClCompile Include="Engine\Mesh\SphereMesh.cpp" />
        <ClCompile Include="Engine\Mesh\TorusMesh.cpp" />
        <ClCompile Include="Engine\Platform\Android\AndroidEngine.cpp" />
        <ClCompile Include="Engine\Platform\IOS\IosEngine.cpp" />
        <ClCompile Include="Engine\Platform\Linux\LinuxEngine.cpp" />
        <ClCompile Include="Engine\Platform\Mac\MacEngine.cpp" />
        <ClCompile Include="Engine\Platform\PlayStation\PlayStationEngine.cpp" />
        <ClCompile Include="Engine\Platform\Windows\WindowsEngine.cpp" />
        <ClCompile Include="Engine\Platform\Windows\WindowsMessageProcessing.cpp" />
        <ClCompile Include="Engine\Platform\XBox\XboxOnceEngine.cpp" />
        <ClCompile Include="Engine\Rendering\Core\Buffer\ConstructBuffer.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\AmbientOcclusion\ScreenSpace\ScreenSpaceAmbientOcclusion.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\AmbientOcclusion\ScreenSpace\SSAOType.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\ConstantBuffer\ConstantBufferViews.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\DescriptorHeap\DirectXDescriptorHeap.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\DynamicMap\Core\DynamicCubeMap.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\DynamicMap\Core\DynamicMap.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\DynamicMap\CubeMap\DynamicReflectionCubeMap.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\DynamicMap\ShadowMap\DynamicShadowCubeMap.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\DynamicMap\ShadowMap\DynamicShadowMap.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\Geometry\GeometryMap.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\Geometry\RenderingData.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\PipelineState\DirectXPipelineState.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderBuffer\AmbientBuffer.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderBuffer\Core\RenderBuffer.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderBuffer\DepthBuffer.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderBuffer\NoiseBuffer.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderBuffer\NormalBuffer.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderBuffer\SampleVolumeBuffer.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderingPipeline.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderingPipelineType.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\BufferRenderLayer\NormalBufferRenderLayer.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\Core\RenderLayer.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\AlphaTestRenderLayer.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\BackgroundRenderLayer.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\OpaqueReflectorRenderLayer.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\OpaqueRenderLayer.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\OpaqueShadowRenderLayer.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\OpaqueSkinnedRenderLayer.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\OperationHandleRenderLayer.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\OperationHandleRotPlaneRenderLayer.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\SelectRenderLayer.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\SSAOBilateralRenderLayer.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\SSAORenderLayer.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\TransparentRenderLayer.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayerManage.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderTarget\BufferRenderTarget.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderTarget\Core\RenderTarget.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderTarget\CubeMapRenderTarget.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderTarget\ShadowMapRenderTarget.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RootSignature\Core\DirectXRootSignature.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RootSignature\DefaultDirectXRootSignature.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RootSignature\SSAODirectXRootSignature.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\StaticSampler\StaticSamplerObject.cpp" />
        <ClCompile Include="Engine\Rendering\Core\DirectX\RenderingPipeline\UI\IMGUIPipeline.cpp" />
        <ClCompile Include="Engine\Rendering\Core\Rendering.cpp" />
        <ClCompile Include="Engine\Rendering\Core\RenderingResourcesUpdate.cpp" />
        <ClCompile Include="Engine\Rendering\Core\RenderingTextureResourcesUpdate.cpp" />
        <ClCompile Include="Engine\Rendering\DirectX12\DDSTextureLoader.cpp" />
        <ClCompile Include="Engine\Rendering\Enigne\Core\RenderingEngine.cpp" />
        <ClCompile Include="Engine\Rendering\Enigne\DirectX\Core\DirectXRenderingEngine.cpp" />
        <ClCompile Include="Engine\Rendering\Enigne\DirectX\DirectX11RenderingEngine.cpp" />
        <ClCompile Include="Engine\Rendering\Enigne\DirectX\DirectX12RenderingEngine.cpp" />
        <ClCompile Include="Engine\Rendering\Enigne\Metal\MetalRenderingEngine.cpp" />
        <ClCompile Include="Engine\Rendering\Enigne\OpenGL\OpenGLESRenderingEngine.cpp" />
        <ClCompile Include="Engine\Rendering\Enigne\Vulkan\VulkanRenderingEngine.cpp" />
        <ClCompile Include="Engine\RenZhaiEngine.cpp" />
        <ClCompile Include="Engine\Shader\Core\Shader.cpp" />
        <ClCompile Include="Engine\Shader\Core\ShaderType.cpp" />
        <ClCompile Include="Engine\Timer.cpp" />
        <ClCompile Include="imgui\backends\imgui_impl_dx12.cpp" />
        <ClCompile Include="imgui\backends\imgui_impl_win32.cpp" />
        <ClCompile Include="imgui\imgui.cpp" />
        <ClCompile Include="imgui\imgui_demo.cpp" />
        <ClCompile Include="imgui\imgui_draw.cpp" />
        <ClCompile Include="imgui\imgui_tables.cpp" />
        <ClCompile Include="imgui\imgui_widgets.cpp" />
        <ClCompile Include="imgui\libs\usynergy\uSynergy.c" />
        <ClCompile Include="LinkCodeReflection.build.cpp" />
    </ItemGroup>
    <ItemGroup>
        <ClInclude Include="Common\EngineVariableTable.h" />
        <ClInclude Include="Common\OperationHandleSelectManage.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\BlueprintCompile\BlueprintCompiledStatement.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\BlueprintCompile\BlueprintCompilerContext.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\BlueprintCompile\BlueprintFunctionContext.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\BlueprintCompile\CompilerBytecodeBackend.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\BlueprintCompile\ScriptBuilder.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\BlueprintCompile\Terminal.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\BlueprintConfigInfo.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\BlueprintFunctionLibrary.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\Core\BlueprintConnection\BlueprintConnectionExtend\BlueprintBezierCubicConnection.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\Core\BlueprintConnection\BlueprintConnectionExtend\BlueprintBezierQuadraticConnection.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\Core\BlueprintConnection\BlueprintConnectionExtend\BlueprintLineConnection.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\Core\BlueprintConnection\BlueprintConnectionManage.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\Core\BlueprintConnection\BlueprintConnectionType.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\Core\BlueprintConnection\BlueprintDrawConnection.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\Core\BlueprintConnection\Interface\BlueprintConnection.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\Core\BlueprintEditor\BlueprintEditor.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\Core\BlueprintEditorType.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\Core\BlueprintEditorUnrealEngineType.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\Core\BlueprintGraphic\GraphicBlueprintEditor.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\Core\BlueprintMenu\BlueprintNodeMenuEditor.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\Core\BlueprintNode\BlueprintNode.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\Core\BlueprintNode\BlueprintPin.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\Core\BlueprintNode\Node.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\Core\BlueprintSAD\BlueprintDeserialization\BlueprintDeserialization.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\Core\BlueprintSAD\BlueprintSADType.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\Core\BlueprintSAD\BlueprintSerialize\BlueprintSerialize.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\Core\BlueprintToolbarEidtor\BlueprintToolbarEditor.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\Core\BlueprintToolButton\BlueprintToolButtonEditor.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Details\ObjectDetailsEditor.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Graphic\ObjectGraphicBlueprintEditor.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Menu\ObjectBlueprintNodeMenuEditor.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Node\BlueprintNode\EventBlueprintNode.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Node\BlueprintNode\FunctionBlueprintNode.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Node\BlueprintNode\ObjectBlueprintNode.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Node\BlueprintNode\PureFunctionBlueprintNode.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Node\BlueprintPin\BoolBlueprintPin.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Node\BlueprintPin\FloatBlueprintPin.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Node\BlueprintPin\IntBlueprintPin.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Node\BlueprintPin\ObjectBlueprintPin.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Node\BlueprintPin\StringBlueprintPin.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Node\BlueprintPin\VectorBlueprintPin.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\ObjectBlueprintEditor.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\Toolbar\ObjectBlueprintToolbarEditor.h" />
        <ClInclude Include="EditorEngine\BlueprintEditor\ObjectBlueprintEditor\ToolButton\ObjectBlueprintToolButtonEditor.h" />
        <ClInclude Include="EditorEngine\BrowseEditor\BrowseEditor.h" />
        <ClInclude Include="EditorEngine\BrowseEditor\Core\AssetEditorCommon.h" />
        <ClInclude Include="EditorEngine\BrowseEditor\Core\AssetFileDirectory.h" />
        <ClInclude Include="EditorEngine\BrowseEditor\Core\BrowseEditorType.h" />
        <ClInclude Include="EditorEngine\BrowseEditor\Editor\AssetEditor.h" />
        <ClInclude Include="EditorEngine\BrowseEditor\Editor\Element\BrowseAssetIcon.h" />
        <ClInclude Include="EditorEngine\BrowseEditor\Editor\FileEditor.h" />
        <ClInclude Include="EditorEngine\BrowseEditor\Editor\Menu\AssetMenuEditor.h" />
        <ClInclude Include="EditorEngine\BrowseEditor\Editor\Menu\FolderMenuEditor.h" />
        <ClInclude Include="EditorEngine\BrowseEditor\Editor\Menu\ObjectAssetMenuEditor.h" />
        <ClInclude Include="EditorEngine\Core\EditorBase.h" />
        <ClInclude Include="EditorEngine\Core\EditorCommon.h" />
        <ClInclude Include="EditorEngine\DetailsEditor\Core\ClassDetailsMapping.h" />
        <ClInclude Include="EditorEngine\DetailsEditor\Core\ConstructDetailsMacro.h" />
        <ClInclude Include="EditorEngine\DetailsEditor\Core\ConstructDetailsWidget.h" />
        <ClInclude Include="EditorEngine\DetailsEditor\Core\DetailsMapping.h" />
        <ClInclude Include="EditorEngine\DetailsEditor\Core\PropertyDetailsMapping.h" />
        <ClInclude Include="EditorEngine\DetailsEditor\Core\RegisterDetailsMapping.h" />
        <ClInclude Include="EditorEngine\DetailsEditor\DetailsEditor.h" />
        <ClInclude Include="EditorEngine\DetailsEditor\Mappings\ActorObjectDetailsMapping.h" />
        <ClInclude Include="EditorEngine\DetailsEditor\Mappings\ArrayDetailsMapping.h" />
        <ClInclude Include="EditorEngine\DetailsEditor\Mappings\BoolDetailsMapping.h" />
        <ClInclude Include="EditorEngine\DetailsEditor\Mappings\ContainerDetailsMapping.h" />
        <ClInclude Include="EditorEngine\DetailsEditor\Mappings\FloatDetailsMapping.h" />
        <ClInclude Include="EditorEngine\DetailsEditor\Mappings\IntDetailsMapping.h" />
        <ClInclude Include="EditorEngine\DetailsEditor\Mappings\MapDetailsMapping.h" />
        <ClInclude Include="EditorEngine\DetailsEditor\Mappings\RotatorDetailsMapping.h" />
        <ClInclude Include="EditorEngine\DetailsEditor\Mappings\StringDetailsMapping.h" />
        <ClInclude Include="EditorEngine\DetailsEditor\Mappings\Vector3DDetailsMapping.h" />
        <ClInclude Include="EditorEngine\DetailsEditor\Mappings\XMFLOAT3DetailsMapping.h" />
        <ClInclude Include="EditorEngine\DetailsEditor\OutsideDetailsEditor.h" />
        <ClInclude Include="EditorEngine\EditorEngine.h" />
        <ClInclude Include="EditorEngine\LogEditor\LogEditor.h" />
        <ClInclude Include="EditorEngine\LogEditor\LogObject\LogSystem.h" />
        <ClInclude Include="EditorEngine\OutLineEditor\OutLineEditor.h" />
        <ClInclude Include="EditorEngine\SelectEditor\OperationHandle\Core\OperationHandleBase.h" />
        <ClInclude Include="EditorEngine\SelectEditor\OperationHandle\MoveArrow.h" />
        <ClInclude Include="EditorEngine\SelectEditor\OperationHandle\RotatorArrow.h" />
        <ClInclude Include="EditorEngine\SelectEditor\OperationHandle\ScalingArrow.h" />
        <ClInclude Include="EditorEngine\ToolbarEditor\ToolbarEditor.h" />
        <ClInclude Include="Engine\Actor\Core\ActorObject.h" />
        <ClInclude Include="Engine\Actor\Light\Core\Light.h" />
        <ClInclude Include="Engine\Actor\Light\Core\RangeLight.h" />
        <ClInclude Include="Engine\Actor\Light\ParallelLight.h" />
        <ClInclude Include="Engine\Actor\Light\PointLight.h" />
        <ClInclude Include="Engine\Actor\Light\SpotLight.h" />
        <ClInclude Include="Engine\Actor\Sky\Fog.h" />
        <ClInclude Include="Engine\Actor\Sky\Sky.h" />
        <ClInclude Include="Engine\Animation\Core\AnimationSequence.h" />
        <ClInclude Include="Engine\Animation\Core\BoneAnimation.h" />
        <ClInclude Include="Engine\Animation\Core\SkinnedAnimationData.h" />
        <ClInclude Include="Engine\Animation\Skeleton\Bone.h" />
        <ClInclude Include="Engine\Animation\Skeleton\Skeleton.h" />
        <ClInclude Include="Engine\Animation\SkinnedAnimationInstance.h" />
        <ClInclude Include="Engine\Collision\CollisionSceneQuery.h" />
        <ClInclude Include="Engine\Component\Input\Input.h" />
        <ClInclude Include="Engine\Component\Input\InputType.h" />
        <ClInclude Include="Engine\Component\InputComponent.h" />
        <ClInclude Include="Engine\Component\Light\Core\LightComponent.h" />
        <ClInclude Include="Engine\Component\Light\Core\LightConstantBuffer.h" />
        <ClInclude Include="Engine\Component\Light\Core\LightType.h" />
        <ClInclude Include="Engine\Component\Light\Core\RangeLightComponent.h" />
        <ClInclude Include="Engine\Component\Light\ParallelLightComponent.h" />
        <ClInclude Include="Engine\Component\Light\PointLightComponent.h" />
        <ClInclude Include="Engine\Component\Light\SpotLightComponent.h" />
        <ClInclude Include="Engine\Component\Mesh\BoxMeshComponent.h" />
        <ClInclude Include="Engine\Component\Mesh\ConeMeshComponent.h" />
        <ClInclude Include="Engine\Component\Mesh\Core\MeshAssist.h" />
        <ClInclude Include="Engine\Component\Mesh\Core\MeshComponent.h" />
        <ClInclude Include="Engine\Component\Mesh\Core\MeshComponentType.h" />
        <ClInclude Include="Engine\Component\Mesh\Core\ShellMeshComponent.h" />
        <ClInclude Include="Engine\Component\Mesh\CustomMeshComponent.h" />
        <ClInclude Include="Engine\Component\Mesh\CylinderMeshComponent.h" />
        <ClInclude Include="Engine\Component\Mesh\PipeMeshComponent.h" />
        <ClInclude Include="Engine\Component\Mesh\PlaneMeshComponent.h" />
        <ClInclude Include="Engine\Component\Mesh\PyramidMeshComponent.h" />
        <ClInclude Include="Engine\Component\Mesh\SkinnedMeshComponent.h" />
        <ClInclude Include="Engine\Component\Mesh\SphereMeshComponent.h" />
        <ClInclude Include="Engine\Component\Mesh\TorusMeshComponent.h" />
        <ClInclude Include="Engine\Component\Sky\FogComponent.h" />
        <ClInclude Include="Engine\Component\Sky\SkyConstantBuffer.h" />
        <ClInclude Include="Engine\Component\TimelineComponent.h" />
        <ClInclude Include="Engine\Component\TransformationComponent.h" />
        <ClInclude Include="Engine\Config\EngineRenderConfig.h" />
        <ClInclude Include="Engine\Core\Camera.h" />
        <ClInclude Include="Engine\Core\CameraType.h" />
        <ClInclude Include="Engine\Core\Construction\ActorMeshConstruction.h" />
        <ClInclude Include="Engine\Core\Construction\MacroConstruction.h" />
        <ClInclude Include="Engine\Core\Construction\MeshConstruction.h" />
        <ClInclude Include="Engine\Core\Engine.h" />
        <ClInclude Include="Engine\Core\Viewport\ClientViewport.h" />
        <ClInclude Include="Engine\Core\Viewport\Viewport.h" />
        <ClInclude Include="Engine\Core\Viewport\ViewportInfo.h" />
        <ClInclude Include="Engine\Core\Viewport\ViewportTransformation.h" />
        <ClInclude Include="Engine\Core\WinMainCommandParameters.h" />
        <ClInclude Include="Engine\Core\World.h" />
        <ClInclude Include="Engine\EngineFactory.h" />
        <ClInclude Include="Engine\EngineMacro.h" />
        <ClInclude Include="Engine\EngineMinimal.h" />
        <ClInclude Include="Engine\EngineType.h" />
        <ClInclude Include="Engine\Interface\DirectXDeviceInterfece.h" />
        <ClInclude Include="Engine\Interface\OpenGLDeviceInterfece.h" />
        <ClInclude Include="Engine\Library\Core\BlueprinFunctiontLibrary.h" />
        <ClInclude Include="Engine\Library\EngineMisLibrary.h" />
        <ClInclude Include="Engine\Library\MathLibrary.h" />
        <ClInclude Include="Engine\Library\RaycastSystemLibrary.h" />
        <ClInclude Include="Engine\Manage\LightManage.h" />
        <ClInclude Include="Engine\Manage\ViewportDataManage.h" />
        <ClInclude Include="Engine\Math\EngineMath.h" />
        <ClInclude Include="Engine\Mesh\BoxMesh.h" />
        <ClInclude Include="Engine\Mesh\ConeMesh.h" />
        <ClInclude Include="Engine\Mesh\Core\Material\Material.h" />
        <ClInclude Include="Engine\Mesh\Core\Material\MaterialConstantBuffer.h" />
        <ClInclude Include="Engine\Mesh\Core\Material\MaterialType.h" />
        <ClInclude Include="Engine\Mesh\Core\Mesh.h" />
        <ClInclude Include="Engine\Mesh\Core\MeshManage.h" />
        <ClInclude Include="Engine\Mesh\Core\MeshType.h" />
        <ClInclude Include="Engine\Mesh\Core\ObjectTransformation.h" />
        <ClInclude Include="Engine\Mesh\Core\SkinnedTransformation.h" />
        <ClInclude Include="Engine\Mesh\CustomMesh.h" />
        <ClInclude Include="Engine\Mesh\CylinderMesh.h" />
        <ClInclude Include="Engine\Mesh\PipeMesh.h" />
        <ClInclude Include="Engine\Mesh\PlaneMesh.h" />
        <ClInclude Include="Engine\Mesh\PyramidMesh.h" />
        <ClInclude Include="Engine\Mesh\SkinnedMesh.h" />
        <ClInclude Include="Engine\Mesh\SphereMesh.h" />
        <ClInclude Include="Engine\Mesh\TorusMesh.h" />
        <ClInclude Include="Engine\Platform\Android\AndroidEngine.h" />
        <ClInclude Include="Engine\Platform\IOS\IosEngine.h" />
        <ClInclude Include="Engine\Platform\Linux\LinuxEngine.h" />
        <ClInclude Include="Engine\Platform\Mac\MacEngine.h" />
        <ClInclude Include="Engine\Platform\PlayStation\PlayStationEngine.h" />
        <ClInclude Include="Engine\Platform\Windows\WindowsEngine.h" />
        <ClInclude Include="Engine\Platform\Windows\WindowsMessageProcessing.h" />
        <ClInclude Include="Engine\Platform\Windows\WindowsPlatform.h" />
        <ClInclude Include="Engine\Platform\XBox\XboxOnceEngine.h" />
        <ClInclude Include="Engine\Rendering\Core\Buffer\ConstructBuffer.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\AmbientOcclusion\ScreenSpace\ScreenSpaceAmbientOcclusion.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\AmbientOcclusion\ScreenSpace\SSAOType.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\ConstantBuffer\ConstantBufferViews.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\DescriptorHeap\DirectXDescriptorHeap.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\DynamicMap\Core\DynamicCubeMap.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\DynamicMap\Core\DynamicMap.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\DynamicMap\CubeMap\DynamicReflectionCubeMap.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\DynamicMap\ShadowMap\DynamicShadowCubeMap.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\DynamicMap\ShadowMap\DynamicShadowMap.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\Geometry\GeometryMap.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\Geometry\RenderingData.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\PipelineState\DirectXPipelineState.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderBuffer\AmbientBuffer.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderBuffer\Core\RenderBuffer.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderBuffer\DepthBuffer.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderBuffer\NoiseBuffer.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderBuffer\NormalBuffer.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderBuffer\SampleVolumeBuffer.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderingPipeline.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderingPipelineType.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\BufferRenderLayer\NormalBufferRenderLayer.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\Core\RenderLayer.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\AlphaTestRenderLayer.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\BackgroundRenderLayer.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\OpaqueReflectorRenderLayer.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\OpaqueRenderLayer.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\OpaqueShadowRenderLayer.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\OpaqueSkinnedRenderLayer.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\OperationHandleRenderLayer.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\OperationHandleRotPlaneRenderLayer.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\SelectRenderLayer.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\SSAOBilateralRenderLayer.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\SSAORenderLayer.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayer\TransparentRenderLayer.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderLayer\RenderLayerManage.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderTarget\BufferRenderTarget.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderTarget\Core\RenderTarget.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderTarget\CubeMapRenderTarget.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RenderTarget\ShadowMapRenderTarget.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RootSignature\Core\DirectXRootSignature.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RootSignature\DefaultDirectXRootSignature.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RootSignature\DirectXRootSignatureType.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\RootSignature\SSAODirectXRootSignature.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\StaticSampler\StaticSamplerObject.h" />
        <ClInclude Include="Engine\Rendering\Core\DirectX\RenderingPipeline\UI\IMGUIPipeline.h" />
        <ClInclude Include="Engine\Rendering\Core\Rendering.h" />
        <ClInclude Include="Engine\Rendering\Core\RenderingResourcesUpdate.h" />
        <ClInclude Include="Engine\Rendering\Core\RenderingTextureResourcesUpdate.h" />
        <ClInclude Include="Engine\Rendering\DirectX12\d3dx12.h" />
        <ClInclude Include="Engine\Rendering\DirectX12\DDSTextureLoader.h" />
        <ClInclude Include="Engine\Rendering\Enigne\Core\RenderingEngine.h" />
        <ClInclude Include="Engine\Rendering\Enigne\DirectX\Core\DirectXRenderingEngine.h" />
        <ClInclude Include="Engine\Rendering\Enigne\DirectX\DirectX11RenderingEngine.h" />
        <ClInclude Include="Engine\Rendering\Enigne\DirectX\DirectX12RenderingEngine.h" />
        <ClInclude Include="Engine\Rendering\Enigne\Metal\MetalRenderingEngine.h" />
        <ClInclude Include="Engine\Rendering\Enigne\OpenGL\OpenGLESRenderingEngine.h" />
        <ClInclude Include="Engine\Rendering\Enigne\Vulkan\VulkanRenderingEngine.h" />
        <ClInclude Include="Engine\Shader\Core\Shader.h" />
        <ClInclude Include="Engine\Shader\Core\ShaderType.h" />
        <ClInclude Include="Engine\Timer.h" />
        <ClInclude Include="imgui\backends\imgui_impl_dx12.h" />
        <ClInclude Include="imgui\backends\imgui_impl_win32.h" />
        <ClInclude Include="imgui\imconfig.h" />
        <ClInclude Include="imgui\imgui.h" />
        <ClInclude Include="imgui\imgui_internal.h" />
        <ClInclude Include="imgui\imstb_rectpack.h" />
        <ClInclude Include="imgui\imstb_textedit.h" />
        <ClInclude Include="imgui\imstb_truetype.h" />
        <ClInclude Include="imgui\libs\glfw\include\GLFW\glfw3.h" />
        <ClInclude Include="imgui\libs\glfw\include\GLFW\glfw3native.h" />
        <ClInclude Include="imgui\libs\usynergy\uSynergy.h" />
    </ItemGroup>
    <PropertyGroup Label="Globals">
        <VCProjectVersion>16.0</VCProjectVersion>
        <Keyword>Win32Proj</Keyword>
        <ProjectGuid>{e2d9491c-4ac8-4c0d-b527-fd72e3634400}</ProjectGuid>
        <RootNamespace>RenZhaiEngine</RootNamespace>
        <WindowsTargetPlatformVersion>10.0.19041.0</WindowsTargetPlatformVersion>
    </PropertyGroup>
    <Import Project="$(VCTargetsPath)\Microsoft.Cpp.Default.props" />
    <PropertyGroup Condition="&apos;$(Configuration)|$(Platform)&apos;==&apos;Debug|Win32&apos;" Label="Configuration">
        <ConfigurationType>Application</ConfigurationType>
        <UseDebugLibraries>true</UseDebugLibraries>
        <PlatformToolset>v142</PlatformToolset>
        <CharacterSet>Unicode</CharacterSet>
    </PropertyGroup>
    <PropertyGroup Condition="&apos;$(Configuration)|$(Platform)&apos;==&apos;Release|Win32&apos;" Label="Configuration">
        <ConfigurationType>Application</ConfigurationType>
        <UseDebugLibraries>false</UseDebugLibraries>
        <PlatformToolset>v142</PlatformToolset>
        <WholeProgramOptimization>true</WholeProgramOptimization>
        <CharacterSet>Unicode</CharacterSet>
    </PropertyGroup>
    <PropertyGroup Condition="&apos;$(Configuration)|$(Platform)&apos;==&apos;Debug|x64&apos;" Label="Configuration">
        <ConfigurationType>Application</ConfigurationType>
        <UseDebugLibraries>true</UseDebugLibraries>
        <PlatformToolset>v142</PlatformToolset>
        <CharacterSet>Unicode</CharacterSet>
    </PropertyGroup>
    <PropertyGroup Condition="&apos;$(Configuration)|$(Platform)&apos;==&apos;Release|x64&apos;" Label="Configuration">
        <ConfigurationType>Application</ConfigurationType>
        <UseDebugLibraries>false</UseDebugLibraries>
        <PlatformToolset>v142</PlatformToolset>
        <WholeProgramOptimization>true</WholeProgramOptimization>
        <CharacterSet>Unicode</CharacterSet>
    </PropertyGroup>
    <Import Project="$(VCTargetsPath)\Microsoft.Cpp.props" />
    <ImportGroup Label="ExtensionSettings" />
    <ImportGroup Label="Shared" />
    <ImportGroup Label="PropertySheets" Condition="&apos;$(Configuration)|$(Platform)&apos;==&apos;Debug|Win32&apos;">
        <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists(&apos;$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props&apos;)" Label="LocalAppDataPlatform" />
    </ImportGroup>
    <ImportGroup Label="PropertySheets" Condition="&apos;$(Configuration)|$(Platform)&apos;==&apos;Release|Win32&apos;">
        <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists(&apos;$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props&apos;)" Label="LocalAppDataPlatform" />
    </ImportGroup>
    <ImportGroup Label="PropertySheets" Condition="&apos;$(Configuration)|$(Platform)&apos;==&apos;Debug|x64&apos;">
        <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists(&apos;$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props&apos;)" Label="LocalAppDataPlatform" />
    </ImportGroup>
    <ImportGroup Label="PropertySheets" Condition="&apos;$(Configuration)|$(Platform)&apos;==&apos;Release|x64&apos;">
        <Import Project="$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props" Condition="exists(&apos;$(UserRootDir)\Microsoft.Cpp.$(Platform).user.props&apos;)" Label="LocalAppDataPlatform" />
    </ImportGroup>
    <PropertyGroup Label="UserMacros" />
    <PropertyGroup Condition="&apos;$(Configuration)|$(Platform)&apos;==&apos;Debug|Win32&apos;">
        <LinkIncremental>true</LinkIncremental>
        <LibraryPath>P:\RenZhaiEngine\Debug;$(LibraryPath)</LibraryPath>
        <IncludePath>$(SolutionDir)RenZhaiEngine\imgui;P:\RenZhaiEngine\RenZhaiEngine\SDK\FBX\FBXSDK;$(IncludePath)</IncludePath>
    </PropertyGroup>
    <PropertyGroup Condition="&apos;$(Configuration)|$(Platform)&apos;==&apos;Release|Win32&apos;">
        <LinkIncremental>false</LinkIncremental>
    </PropertyGroup>
    <PropertyGroup Condition="&apos;$(Configuration)|$(Platform)&apos;==&apos;Debug|x64&apos;">
        <LinkIncremental>true</LinkIncremental>
        <IncludePath>$(SolutionDir)Source\RenZhaiCoreObject;$(SolutionDir)Source\RenZhaiMeshImportExportTool;$(SolutionDir)Source\SimpleBaseLibrary;$(SolutionDir)Source\RenZhaiAssetHandle;$(SolutionDir)Source\RenZhaiEngineCore;$(SolutionDir)Source\RenZhaiEngine\imgui;$(SolutionDir)Intermediate\CodeReflection;$(IncludePath)</IncludePath>
        <LibraryPath>$(SolutionDir)Binaries\Win64\;$(LibraryPath)</LibraryPath>
        <OutDir>$(SolutionDir)Binaries\Win64\</OutDir>
        <IntDir>$(SolutionDir)Intermediate\Other\</IntDir>
    </PropertyGroup>
    <PropertyGroup Condition="&apos;$(Configuration)|$(Platform)&apos;==&apos;Release|x64&apos;">
        <LinkIncremental>false</LinkIncremental>
    </PropertyGroup>
    <ItemDefinitionGroup Condition="&apos;$(Configuration)|$(Platform)&apos;==&apos;Debug|Win32&apos;">
        <ClCompile>
            <WarningLevel>Level3</WarningLevel>
            <SDLCheck>true</SDLCheck>
            <PreprocessorDefinitions>WIN32;_DEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
            <ConformanceMode>true</ConformanceMode>
            <LanguageStandard>Default</LanguageStandard>
        </ClCompile>
        <Link>
            <SubSystem>Windows</SubSystem>
            <GenerateDebugInformation>true</GenerateDebugInformation>
            <StackCommitSize />
            <StackReserveSize>100000000</StackReserveSize>
            <HeapReserveSize />
            <HeapCommitSize />
            <AdditionalDependencies>FBXSDK.lib;%(AdditionalDependencies)</AdditionalDependencies>
        </Link>
    </ItemDefinitionGroup>
    <ItemDefinitionGroup Condition="&apos;$(Configuration)|$(Platform)&apos;==&apos;Release|Win32&apos;">
        <ClCompile>
            <WarningLevel>Level3</WarningLevel>
            <FunctionLevelLinking>true</FunctionLevelLinking>
            <IntrinsicFunctions>true</IntrinsicFunctions>
            <SDLCheck>true</SDLCheck>
            <PreprocessorDefinitions>WIN32;NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
            <ConformanceMode>true</ConformanceMode>
        </ClCompile>
        <Link>
            <SubSystem>Console</SubSystem>
            <EnableCOMDATFolding>true</EnableCOMDATFolding>
            <OptimizeReferences>true</OptimizeReferences>
            <GenerateDebugInformation>true</GenerateDebugInformation>
        </Link>
    </ItemDefinitionGroup>
    <ItemDefinitionGroup Condition="&apos;$(Configuration)|$(Platform)&apos;==&apos;Debug|x64&apos;">
        <ClCompile>
            <WarningLevel>Level3</WarningLevel>
            <SDLCheck>true</SDLCheck>
            <PreprocessorDefinitions>_DEBUG;_CONSOLE;FBXSDK_EXPORTS;_CRT_SECURE_NO_WARNINGS;SIMPLE_LIBRARY_EXPORTS;CLOSE_DISABLE_DEBUG_INFOR;EDITOR_ENGINE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
            <ConformanceMode>true</ConformanceMode>
            <MultiProcessorCompilation>true</MultiProcessorCompilation>
        </ClCompile>
        <Link>
            <SubSystem>Windows</SubSystem>
            <GenerateDebugInformation>true</GenerateDebugInformation>
            <HeapReserveSize>10000000</HeapReserveSize>
            <HeapCommitSize>10000000</HeapCommitSize>
            <StackReserveSize>10000000</StackReserveSize>
            <StackCommitSize>10000000</StackCommitSize>
        </Link>
    </ItemDefinitionGroup>
    <ItemDefinitionGroup Condition="&apos;$(Configuration)|$(Platform)&apos;==&apos;Release|x64&apos;">
        <ClCompile>
            <WarningLevel>Level3</WarningLevel>
            <FunctionLevelLinking>true</FunctionLevelLinking>
            <IntrinsicFunctions>true</IntrinsicFunctions>
            <SDLCheck>true</SDLCheck>
            <PreprocessorDefinitions>NDEBUG;_CONSOLE;%(PreprocessorDefinitions)</PreprocessorDefinitions>
            <ConformanceMode>true</ConformanceMode>
        </ClCompile>
        <Link>
            <SubSystem>Console</SubSystem>
            <EnableCOMDATFolding>true</EnableCOMDATFolding>
            <OptimizeReferences>true</OptimizeReferences>
            <GenerateDebugInformation>true</GenerateDebugInformation>
        </Link>
    </ItemDefinitionGroup>
    <Import Project="$(VCTargetsPath)\Microsoft.Cpp.targets" />
    <ImportGroup Label="ExtensionTargets" />
</Project>
