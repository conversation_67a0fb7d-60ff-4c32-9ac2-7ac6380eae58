#include "DefaultDirectXRootSignature.h"

FDefaultDirectXRootSignature::FDefaultDirectXRootSignature()
{

}

void FDefaultDirectXRootSignature::BuildRootSignature(UINT InTextureNum)
{
    //������ǩ��
    CD3DX12_ROOT_PARAMETER RootParam[11];

    // 初始化所有参数为零
    memset(RootParam, 0, sizeof(RootParam));
   
    //texture������
    CD3DX12_DESCRIPTOR_RANGE DescriptorRangeTextureSRV;
    DescriptorRangeTextureSRV.Init(D3D12_DESCRIPTOR_RANGE_TYPE_SRV, 
        InTextureNum, 4);

    //SSAO
    //CD3DX12_DESCRIPTOR_RANGE DescriptorSSAOMapSRV;
    //DescriptorSSAOMapSRV.Init(D3D12_DESCRIPTOR_RANGE_TYPE_SRV, 1, 3);

    //��̬CubeMap
    CD3DX12_DESCRIPTOR_RANGE DescriptorRangeCubeMapSRV;
    DescriptorRangeCubeMapSRV.Init(D3D12_DESCRIPTOR_RANGE_TYPE_SRV, 1, 0);

    //ShadowMap
    CD3DX12_DESCRIPTOR_RANGE DescriptorShadowMapSRV;
    DescriptorShadowMapSRV.Init(D3D12_DESCRIPTOR_RANGE_TYPE_SRV, 1, 2);

    //ShadowCubeMap
    CD3DX12_DESCRIPTOR_RANGE DescriptorShadowCubeMapSRV;
    DescriptorShadowCubeMapSRV.Init(D3D12_DESCRIPTOR_RANGE_TYPE_SRV, 1, 1);

    RootParam[Signature_Object].InitAsConstantBufferView(0);//����
    RootParam[Signature_ObjectSkinned].InitAsConstantBufferView(1);//��Ƥ
    RootParam[Signature_Viewport].InitAsConstantBufferView(2);//�ӿ�
    RootParam[Signature_Light].InitAsConstantBufferView(3);//�ƹ�
    RootParam[Signature_Fog].InitAsConstantBufferView(4);//��

    //t
    RootParam[Signature_Material].InitAsShaderResourceView(0,1);//����

    //2D��ͼ
    RootParam[Signature_Texture2D].InitAsDescriptorTable(1, &DescriptorRangeTextureSRV, D3D12_SHADER_VISIBILITY_PIXEL);

    //CubeMap��ͼ
    RootParam[Signature_CubeMapTexture].InitAsDescriptorTable(1, &DescriptorRangeCubeMapSRV, D3D12_SHADER_VISIBILITY_PIXEL);

    //ShadowMap
    RootParam[Signature_ShadowMap].InitAsDescriptorTable(1, &DescriptorShadowMapSRV, D3D12_SHADER_VISIBILITY_PIXEL);

    //ShadowCubeMap
    RootParam[Signature_ShadowCubeMap].InitAsDescriptorTable(1, &DescriptorShadowCubeMapSRV, D3D12_SHADER_VISIBILITY_PIXEL);

    //SSAO - 已禁用
    //RootParam[Signature_SSAO].InitAsDescriptorTable(1, &DescriptorSSAOMapSRV, D3D12_SHADER_VISIBILITY_PIXEL);
    
    //������̬����
    StaticSamplerObject.BuildStaticSampler();

    CD3DX12_ROOT_SIGNATURE_DESC RootSignatureDesc(
        10,  // 只使用10个参数，跳过SSAO
        RootParam,
        StaticSamplerObject.GetSize(),//��������
        StaticSamplerObject.GetData(),//����PTR
        D3D12_ROOT_SIGNATURE_FLAG_ALLOW_INPUT_ASSEMBLER_INPUT_LAYOUT);

    Engine_Log("Building root signature with %d parameters", 10);

    //����
    ComPtr<ID3DBlob> SerializeRootSignature;
    ComPtr<ID3DBlob> ErrorBlob;
    HRESULT hr = D3D12SerializeRootSignature(
        &RootSignatureDesc,
        D3D_ROOT_SIGNATURE_VERSION_1,
        SerializeRootSignature.GetAddressOf(),
        ErrorBlob.GetAddressOf());

    if (FAILED(hr))
    {
        Engine_Log_Error("Failed to serialize root signature: HRESULT = 0x%08X", hr);
        return;
    }

    if (ErrorBlob)
    {
       char *p = (char*)ErrorBlob->GetBufferPointer();
       Engine_Log_Error("Root signature serialization error: %s", p);
       return;
    }

    if (!SerializeRootSignature.Get())
    {
        Engine_Log_Error("SerializeRootSignature is null");
        return;
    }

    if (!GetD3dDevice())
    {
        Engine_Log_Error("D3D Device is null");
        return;
    }

    Engine_Log("Root signature serialization successful, creating root signature...");

    //����
    hr = GetD3dDevice()->CreateRootSignature(
        0,
        SerializeRootSignature->GetBufferPointer(),
        SerializeRootSignature->GetBufferSize(),
        IID_PPV_ARGS(&RootSignature));

    if (FAILED(hr))
    {
        Engine_Log_Error("Failed to create root signature: HRESULT = 0x%08X", hr);
        return;
    }

    Engine_Log("Root signature created successfully!");
}

void FDefaultDirectXRootSignature::PreDraw(float DeltaTime)
{
    Super::PreDraw(DeltaTime);
}

void FDefaultDirectXRootSignature::Draw(float DeltaTime)
{
}

void FDefaultDirectXRootSignature::PostDraw(float DeltaTime)
{
}
