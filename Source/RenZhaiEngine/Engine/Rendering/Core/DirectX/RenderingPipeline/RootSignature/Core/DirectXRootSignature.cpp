#include "DirectXRootSignature.h"

FDirectXRootSignature::FDirectXRootSignature()
{

}

void FDirectXRootSignature::BuildRootSignature(UINT InTextureNum)
{
   
}

void FDirectXRootSignature::PreDraw(float DeltaTime)
{
    if (RootSignature.Get())
    {
        Engine_Log("Setting root signature to command list");
        GetGraphicsCommandList()->SetGraphicsRootSignature(RootSignature.Get());
        Engine_Log("Root signature set successfully");
    }
    else
    {
        Engine_Log_Error("RootSignature is null in PreDraw - cannot set root signature!");
    }
}

void FDirectXRootSignature::Draw(float DeltaTime)
{
}

void FDirectXRootSignature::PostDraw(float DeltaTime)
{
}
