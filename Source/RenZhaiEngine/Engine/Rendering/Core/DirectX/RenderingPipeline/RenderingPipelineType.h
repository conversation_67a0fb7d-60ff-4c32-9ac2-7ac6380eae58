#pragma once

enum EPipelineState
{
	Transparent = 0,
	AlphaTest,
	Background,
	Reflector,
	GrayModel = 4,
	Wireframe = 5,
	OrthogonalShadow = 6,
	PerspectiveShadow = 7,
	VientianeShadow = 8,
	Select = 9,
	Operation_Handle = 10,
	Operation_Handle_Rot_Plane = 11,
	State_Normal = 20,
	State_SSAO = 21,
	State_SSAO_Bilateral = 22,
	Opaque_Skinned = 23,
};

enum ERenderingConditions
{
	RC_None = 0,//ȫ����Ⱦ
	RC_Shadow,//bCastShadow=true ��Ⱦ������
};