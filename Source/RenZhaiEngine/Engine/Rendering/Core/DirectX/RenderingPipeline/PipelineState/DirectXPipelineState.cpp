#include "DirectXPipelineState.h"
#include "../../../../../Rendering/Enigne/DirectX/Core/DirectXRenderingEngine.h"
#include "../../../../../Platform/Windows/WindowsEngine.h"

FDirectXPipelineState::FDirectXPipelineState()
    :PipelineState(EPipelineState::GrayModel)
{
    PSO.insert(pair<int,ComPtr<ID3D12PipelineState>>(4,ComPtr<ID3D12PipelineState>()));//�߿�
    PSO.insert(pair<int, ComPtr<ID3D12PipelineState>>(5, ComPtr<ID3D12PipelineState>()));//Shader
}

void FDirectXPipelineState::PreDraw(float DeltaTime)
{
    GetGraphicsCommandList()->Reset(GetCommandAllocator().Get(), PSO[(int)PipelineState].Get());
}

void FDirectXPipelineState::Draw(float DeltaTime)
{
    //������̰���
    CaptureKeyboardKeys();
}

void FDirectXPipelineState::PostDraw(float DeltaTime)
{

}

void FDirectXPipelineState::ResetGPSDesc()
{
	memset(&GPSDesc, 0, sizeof(D3D12_GRAPHICS_PIPELINE_STATE_DESC));
}

void FDirectXPipelineState::BindInputLayout(const D3D12_INPUT_ELEMENT_DESC* InInputElementDescs, UINT InSize)
{
    //�����벼��
	GPSDesc.InputLayout.pInputElementDescs = InInputElementDescs;
	GPSDesc.InputLayout.NumElements = InSize;
}

void FDirectXPipelineState::BindRootSignature(ID3D12RootSignature* InRootSignature)
{
    //�󶨸�ǩ��
	GPSDesc.pRootSignature = InRootSignature;
}

void FDirectXPipelineState::BindShader(const FShader& InVertexShader, const FShader& InPixelShader)
{
    //�󶨶�����ɫ������
	LPVOID VSPointer = InVertexShader.GetBufferPointer();
	SIZE_T VSSize = InVertexShader.GetBufferSize();
	LPVOID PSPointer = InPixelShader.GetBufferPointer();
	SIZE_T PSSize = InPixelShader.GetBufferSize();

	if (!VSPointer || VSSize == 0)
	{
		Engine_Log_Error("Vertex shader is invalid or empty");
		return;
	}

	if (!PSPointer || PSSize == 0)
	{
		Engine_Log_Error("Pixel shader is invalid or empty");
		return;
	}

	GPSDesc.VS.pShaderBytecode = reinterpret_cast<BYTE*>(VSPointer);
	GPSDesc.VS.BytecodeLength = VSSize;

	GPSDesc.PS.pShaderBytecode = PSPointer;
	GPSDesc.PS.BytecodeLength = PSSize;
}

void FDirectXPipelineState::BuildParam()
{
    GPSDesc = DefaultGPSDesc;
}

void FDirectXPipelineState::Build(int InPSOType)
{
    if (PSO.find(InPSOType) == PSO.end())
    {
        PSO.insert(pair<int, ComPtr<ID3D12PipelineState>>(InPSOType, ComPtr<ID3D12PipelineState>()));//Shader
    }

    // 检查着色器是否有效
    if (!GPSDesc.VS.pShaderBytecode || GPSDesc.VS.BytecodeLength == 0)
    {
        Engine_Log_Error("Cannot create pipeline state: Vertex shader is invalid or empty");
        return;
    }

    if (!GPSDesc.PS.pShaderBytecode || GPSDesc.PS.BytecodeLength == 0)
    {
        Engine_Log_Error("Cannot create pipeline state: Pixel shader is invalid or empty");
        return;
    }

    if (!GPSDesc.pRootSignature)
    {
        Engine_Log_Error("Cannot create pipeline state: Root signature is null");
        return;
    }

    //�߿�ģ��ע��
    HRESULT hr = GetD3dDevice()->CreateGraphicsPipelineState(&GPSDesc, IID_PPV_ARGS(&PSO[InPSOType]));
    if (FAILED(hr))
    {
        Engine_Log_Error("Failed to create graphics pipeline state: HRESULT = 0x%08X", hr);
        return;
    }

   ////ʵ��ģ��ע��
   //GPSDesc.RasterizerState.FillMode = D3D12_FILL_MODE_SOLID;//��ʵ�巽ʽ��ʾ
   //ANALYSIS_HRESULT(GetD3dDevice()->CreateGraphicsPipelineState(&GPSDesc, IID_PPV_ARGS(&PSO[(int)GrayModel])))
}

void FDirectXPipelineState::ResetPSO(int InPSOType)
{
    GetGraphicsCommandList()->SetPipelineState(PSO[InPSOType].Get());
}

void FDirectXPipelineState::ResetPSO()
{
    ResetPSO(PipelineState);
}

void FDirectXPipelineState::SetFillMode(bool bWireframe)
{
    GPSDesc.RasterizerState.FillMode = bWireframe ? D3D12_FILL_MODE_WIREFRAME : D3D12_FILL_MODE_SOLID;
}

void FDirectXPipelineState::SetRenderTarget(int Index, const D3D12_RENDER_TARGET_BLEND_DESC& InRenderTargetBlend)
{
    GPSDesc.BlendState.RenderTarget[Index] = InRenderTargetBlend;
}

void FDirectXPipelineState::SetRasterizerState(const CD3DX12_RASTERIZER_DESC& InRasterizerDesc)
{
    GPSDesc.RasterizerState = InRasterizerDesc;
}

void FDirectXPipelineState::SetDepthStencilState(const CD3DX12_DEPTH_STENCIL_DESC& InDepthStencilDesc)
{
    GPSDesc.DepthStencilState = InDepthStencilDesc;
}

void FDirectXPipelineState::SaveGPSDescAsDefault()
{
    //���ù�դ��״̬
    GPSDesc.RasterizerState = CD3DX12_RASTERIZER_DESC(D3D12_DEFAULT);
    GPSDesc.RasterizerState.FillMode = D3D12_FILL_MODE_SOLID;//�Թ��巽ʽ��ʾ

    //0000..0000
    GPSDesc.SampleMask = UINT_MAX;

    GPSDesc.PrimitiveTopologyType = D3D12_PRIMITIVE_TOPOLOGY_TYPE_TRIANGLE;
    GPSDesc.NumRenderTargets = 1;

    GPSDesc.BlendState = CD3DX12_BLEND_DESC(D3D12_DEFAULT);
    GPSDesc.DepthStencilState = CD3DX12_DEPTH_STENCIL_DESC(D3D12_DEFAULT);

    GPSDesc.SampleDesc.Count = GetEngine()->GetRenderingEngine()->GetDXGISampleCount();
    GPSDesc.SampleDesc.Quality = GetEngine()->GetRenderingEngine()->GetDXGISampleQuality();

    //RTV �� DSV��ʽ
    GPSDesc.RTVFormats[0] = GetEngine()->GetRenderingEngine()->GetBackBufferFormat();
    GPSDesc.DSVFormat = GetEngine()->GetRenderingEngine()->GetDepthStencilFormat();

    DefaultGPSDesc = GPSDesc;
}

void FDirectXPipelineState::CaptureKeyboardKeys()
{
    if (GetAsyncKeyState('4') & 0x8000)
    {
        PipelineState = EPipelineState::Wireframe;
    }
    else if (GetAsyncKeyState('5') & 0x8000)
    {
        PipelineState = EPipelineState::GrayModel;
    }
}
