#include "ViewportDataManage.h"
#include "../Config/EngineRenderConfig.h"

FViewportDataManage::FViewportDataManage()
	:Width(FEngineRenderConfig::GetRenderConfig()-><PERSON>rrenWidth)
	,Height(FEngineRenderConfig::GetRenderConfig()->ScrrenHight)
	,YFOV(0.25f * XM_PI)
	,Aspect((float)Width / (float)Height)
	,<PERSON><PERSON>ear(0.1f)
	,ZFar(10000.f)
{

}

void FViewportDataManage::ResetSize(int InWidth, int InHeight)
{
	Width = InWidth;
	Height = InHeight;

	Aspect = (float)InWidth / (float)InHeight;
}
