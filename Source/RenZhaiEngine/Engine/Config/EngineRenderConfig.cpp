#include "EngineRenderConfig.h"

FEngineRenderConfig* FEngineRenderConfig::RenderConfig = nullptr;

FEngineRenderConfig::FEngineRenderConfig()
	:<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>(1920)
	,<PERSON>rrenH<PERSON>(1080)
	,<PERSON><PERSON>reshR<PERSON>(60)
	,<PERSON>wa<PERSON><PERSON><PERSON>nCount(2)
{
}

FEngineRenderConfig* FEngineRenderConfig::GetRenderConfig()
{
	if (!RenderConfig)
	{
		RenderConfig = new FEngineRenderConfig();
	}

	return RenderConfig;
}

void FEngineRenderConfig::Destroy()
{
	if (RenderConfig)
	{
		delete RenderConfig;
		RenderConfig = nullptr;
	}
}
