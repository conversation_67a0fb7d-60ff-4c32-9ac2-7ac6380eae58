#include "MathLibrary.h"

GMathLibrary::GMathLibrary()
{

}

float GMathLibrary::FloatAddFloat(float A, float B)
{
	return A + B;
}

int GMathLibrary::IntAddFloat(int A, float B)
{
	return A + B;
}

int GMathLibrary::IntAddInt(int A, int B)
{
	return A + B;
}

float GMathLibrary::FloatAddInt(float A, int B)
{
	return A + B;
}


float GMathLibrary::FloatSubtractFloat(float A, float B)
{
	return A - B;
}

int GMathLibrary::IntSubtractFloat(int A, float B)
{
	return A - B;
}

int GMathLibrary::IntSubtractInt(int A, int B)
{
	return A - B;
}

float GMathLibrary::FloatSubtractInt(float A, int B)
{
	return A - B;
}


float GMathLibrary::FloatDivideFloat(float A, float B)
{
	return A / B;
}

int GMathLibrary::IntDivideFloat(int A, float B)
{
	return A / B;
}

int GMathLibrary::IntDivideInt(int A, int B)
{
	return A / B;
}

float GMathLibrary::FloatDivideInt(float A, int B)
{
	return A / B;
}



float GMathLibrary::FloatMultiplyFloat(float A, float B)
{
	return A * B;
}

int GMathLibrary::IntMultiplyFloat(int A, float B)
{
	return A * B;
}

int GMathLibrary::IntMultiplyInt(int A, int B)
{
	return A * B;
}

float GMathLibrary::FloatMultiplyInt(float A, int B)
{
	return A * B;
}