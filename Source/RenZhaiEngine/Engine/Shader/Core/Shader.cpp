#include "Shader.h"

LPVOID FShader::GetBufferPointer() const
{
	if (ShaderCode.Get())
	{
		return ShaderCode->GetBufferPointer();
	}
	return nullptr;
}

SIZE_T FShader::GetBufferSize() const
{
	if (ShaderCode.Get())
	{
		return ShaderCode->GetBufferSize();
	}
	return 0;
}

void FShader::BuildShaders(
	const wstring& InFileName,
	const string& InEntryFunName,
	const string& InShadersVersion,
	const D3D_SHADER_MACRO* InShaderMacro)
{
	wchar_t WStringFileName[1024] = { 0 };
	{
		char WToCBuff[1024] = { 0 };
		wchar_t_to_char(WToCBuff,1024,InFileName.c_str());

		char Buff[1024] = { 0 };
		char *FullPath = get_full_path(Buff,1024, WToCBuff);

		Engine_Log("Shader path: [%s]", FullPath);
	
		char_to_wchar_t(WStringFileName,1024,FullPath);
	}

	ComPtr<ID3DBlob> ErrorShaderMsg;
	HRESULT R = D3DCompileFromFile(WStringFileName,
		InShaderMacro,D3D_COMPILE_STANDARD_FILE_INCLUDE,
		InEntryFunName.c_str(), InShadersVersion.c_str(),
#if _DEBUG
		D3DCOMPILE_DEBUG | D3DCOMPILE_SKIP_OPTIMIZATION
#else
0
#endif
		,0,&ShaderCode,&ErrorShaderMsg);

	if (ErrorShaderMsg)
	{
		char* p = (char*)ErrorShaderMsg->GetBufferPointer();
		if (SUCCEEDED(R))
		{
			Engine_Log_Warning("%s", p);
		}
		else
		{
			Engine_Log_Error("%s", p);
		}

		//������־
		//open_url(get_log_filename());
	}

	//ʧ�ܾͱ�����
	//ANALYSIS_HRESULT(R); // 暂时禁用断言以避免崩溃
	if (FAILED(R))
	{
		Engine_Log_Error("Shader compilation failed with HRESULT: 0x%08X", R);
		// 不再触发断言，让程序继续运行
	}
}