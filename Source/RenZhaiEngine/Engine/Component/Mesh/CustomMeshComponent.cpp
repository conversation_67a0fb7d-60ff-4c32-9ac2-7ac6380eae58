#include "CustomMeshComponent.h"
#include "../../Mesh/Core/MeshType.h"
#include "../../Mesh/Core/Material/Material.h"
#include "../../Core/Construction/MacroConstruction.h"
#include "Core/MeshAssist.h"

CCustomMeshComponent::CCustomMeshComponent()
{

}

void CCustomMeshComponent::CreateMesh(
	FMeshRenderingData& MeshData, 
	const string& InPath,
	const FIEParam& InParam)
{
	//C:/dd/dd/c/x.obj
	//x.obj
	char Buff[1024] = { 0 };
	get_path_clean_filename(Buff,InPath.c_str());

	if (find_string(Buff, ".obj", 0) != -1 ||
		find_string(Buff, ".OBJ", 0) != -1)
	{
		//�õ��ļ���С
		unsigned int FileSize = get_file_size_by_filename(InPath.c_str());

		if (FileSize > 0)
		{
			//�����ļ���С����buff
			char* Buff = new char[FileSize + 1];
			//����Ҫ��ʼ��
			memset(Buff, 0, FileSize + 1);

			//��ȡbuff
			get_file_buf(InPath.c_str(), Buff);

			if (!LoadObjFromBuff(Buff, FileSize, MeshData))
			{

			}

			delete Buff;
		}
	}
	else if (find_string(Buff, ".fbx", 0) != -1 ||
		find_string(Buff, ".FBX", 0) != -1)
	{
		char PathBuff[1024] = { 0 };
		get_full_path(PathBuff,1024, InPath.c_str());

		FMeshAssist::LoadFBXMeshFromBuff(this, InPath, MeshData, InParam);
	}
}

bool CCustomMeshComponent::LoadObjFromBuff(char* InBuff, uint32_t InBuffSize, FMeshRenderingData& MeshData)
{
	if (InBuffSize > 0)
	{
		MeshData.SectionDescribe.push_back(FMeshSection());
		FMeshSection& Section = MeshData.SectionDescribe[MeshData.SectionDescribe.size() - 1];

		stringstream BuffStream(InBuff);
		char TmpLine[256] = { 0 };
		string MidTmpTag;

		vector<XMFLOAT3> Position;
		vector<XMFLOAT3> Normal;
		vector<XMFLOAT3> UTangent;
		vector<XMFLOAT2> TexCoord;//��������

		for (; !BuffStream.eof();)
		{
			memset(TmpLine, 0, 256);

			//��ȡһ������
			BuffStream.getline(TmpLine, 256);
			if (strlen(TmpLine) > 0)
			{
				if (TmpLine[0] == 'v')
				{
					stringstream LineStream(TmpLine);
					LineStream >> MidTmpTag;

					if (TmpLine[1] == 'n')
					{
						//�õ�λ��
						Normal.push_back(XMFLOAT3());
						XMFLOAT3& Float3InNormal = Normal[Normal.size() - 1];

						//������λ��
						LineStream >> Float3InNormal.x;
						LineStream >> Float3InNormal.y;
						LineStream >> Float3InNormal.z;
					}
					else if (TmpLine[1] == 't')
					{	
						TexCoord.push_back(XMFLOAT2());
						XMFLOAT2& Float2InTexCoord = TexCoord[TexCoord.size() - 1];

						LineStream >> Float2InTexCoord.x;
						LineStream >> Float2InTexCoord.y;
					}
					else
					{
						//�õ�λ��
						Position.push_back(XMFLOAT3());
						XMFLOAT3& Float3InPos = Position[Position.size() - 1];

						//������λ��
						LineStream >> Float3InPos.x;
						LineStream >> Float3InPos.y;
						LineStream >> Float3InPos.z;
					}
				}
				else if (TmpLine[0] == 'f')
				{
					stringstream LineStream(TmpLine);
					LineStream >> MidTmpTag;

					char SaveLineString[256] = { 0 };
					char TmpBuff[256] = { 0 };
					for (size_t i = 0; i < 3; i++)
					{
						memset(SaveLineString, 0, 256);

						//����һ������
						LineStream >> SaveLineString;

						//�ҵ�������λ��
						int StringPosA = find_string(SaveLineString, "/", 0);
						memset(TmpBuff, 0, 256);
						char* VPosIndex = string_mid(SaveLineString, TmpBuff, 0, StringPosA);

						//�ŵ�������������
						MeshData.Data.IndexData.push_back(atoi(VPosIndex) - 1);

						//����Index
						int StringPosB = find_string(SaveLineString, "/", StringPosA + 1);
						memset(TmpBuff, 0, 256);
						char* TexcoordIndex = string_mid(SaveLineString, TmpBuff, StringPosA + 1, StringPosB - (StringPosA + 1));

						//����index
						memset(TmpBuff, 0, 256);
						char* NormalIndex = string_mid(SaveLineString, TmpBuff, StringPosB + 1, strlen(SaveLineString) - (StringPosB + 1));
					}
				}
			}
		}

		MeshData.Data.VertexData.resize((int)Position.size());
		for (size_t i = 0; i < Position.size(); i++)
		{
			MeshData.Data.VertexData[i].Position = Position[i];

			// 安全访问法线数据
			if (i < Normal.size())
			{
				MeshData.Data.VertexData[i].Normal = Normal[i];
			}
			else
			{
				MeshData.Data.VertexData[i].Normal = XMFLOAT3(0.0f, 1.0f, 0.0f); // 默认法线
			}

			// 安全访问纹理坐标数据
			if (i < TexCoord.size())
			{
				MeshData.Data.VertexData[i].TexCoord = TexCoord[i];
			}
			else
			{
				MeshData.Data.VertexData[i].TexCoord = XMFLOAT2(0.0f, 0.0f); // 默认纹理坐标
			}

			MeshData.Data.VertexData[i].Color = XMFLOAT4(Colors::White);

			//if (i > 1)
			//{
			//	XMFLOAT3 LastPos = Position[i - 1];
			//	XMFLOAT3 Pos = Position[i];
			//
			//	fvector_3d LastPos3D = EngineMath::ToVector3d(LastPos);
			//	fvector_3d PosVector3D = EngineMath::ToVector3d(Pos);
			//
			//	fvector_3d NewDir = LastPos3D - PosVector3D;
			//	MeshData.VertexData[i].UTangent = XMFLOAT3(NewDir.x, NewDir.y, NewDir.z);
			//}
			//else
			//{
			//	MeshData.VertexData[i].UTangent = XMFLOAT3(0.f, 1.f, 0.f);
			//}
		}

		Section.IndexSize = MeshData.Data.IndexData.size();
		Section.VertexSize = MeshData.Data.VertexData.size();

		SpawnDefaultMaterial();

		return true;
	}

	return false;
}

void CCustomMeshComponent::BuildKey(size_t& OutHashKey, const std::string& InPath, const FIEParam& InParam)
{
	std::hash<string> FloatHash;
	
	OutHashKey = 3;
	OutHashKey += FloatHash(InPath);
}