#pragma once

enum EMeshRenderLayerType
{
	<PERSON><PERSON><PERSON><PERSON><PERSON>YER_OPAQUE = 0,
	R<PERSON><PERSON><PERSON><PERSON>Y<PERSON>_TRANSPARENT,
	RENDERLAYER_ALPHATEST,
	RENDERLAYER_BACKGROUND,
	RENDERLAYER_OPAQUE_REFLECTOR = 4,
	<PERSON><PERSON><PERSON><PERSON><PERSON>YER_SHADOW_RENDER = 8,
	R<PERSON><PERSON><PERSON><PERSON>YER_SELECT = 9,
	R<PERSON><PERSON>RLAYER_OPERATION_HANDLE = 10,
	<PERSON><PERSON><PERSON><PERSON><PERSON>YER_OPERATION_HANDLE_ROT_PLANE=11,
	RENDERLAYER_NORMAL = 20,
	RENDERLAYER_SSAO = 21,
	RENDERLAYER_SSAO_BILATERAL_BLUR = 22,
	<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>_SKINNED_OPAQUE = 23,
};