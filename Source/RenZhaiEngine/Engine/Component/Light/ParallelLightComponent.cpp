#include "ParallelLightComponent.h"
#include "../../Mesh/Core/MeshManage.h"
#include "../../Component/Mesh/Core/MeshComponent.h"
#include "../../Mesh/Core/Material/Material.h"
#include "../../Core/Construction/MacroConstruction.h"

CParallelLightComponent::CParallelLightComponent()
	:Super()
{
	//��ȡģ����Դ
	string MeshPath = FEnginePathHelper::GetEngineContentPath() + "/SunMesh.obj";

	BUILD_OBJECT_PARAMETERS_BY_COMPONENT(, this);

	FIEParam IEParam;
	IEParam.bOriginalCoordinate = false;
	SetLightMesh(GetMeshManage()->CreateMeshComponent(Param,MeshPath, IEParam));

	//����̫��Ϊ�߿�ģʽ
	if (GetLightMesh())
	{
		vector<CMaterial*>* Materials = GetLightMesh()->GetMaterials();
		if (Materials && !Materials->empty())
		{
			if (CMaterial *InMaterial = (*Materials)[0])
			{
				InMaterial->SetMaterialType(EMaterialType::BaseColor);
				InMaterial->SetMaterialDisplayStatus(EMaterialDisplayStatusType::WireframeDisplay);
				InMaterial->SetBaseColor(fvector_4d(1.0f,0.7f,1.0f,1.0f));
			}
		}
	}

	LightType = ELightType::DirectionalLight;
}
