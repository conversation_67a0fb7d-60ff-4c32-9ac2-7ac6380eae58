/****************************************************************************************
 
   Copyright (C) 2015 Autodesk, Inc.
   All rights reserved.
 
   Use of this software is subject to the terms of the Autodesk license agreement
   provided at the time of installation or download, or which otherwise accompanies
   this software in either electronic or hard copy form.
 
****************************************************************************************/

//! \file fbxcharacternodename.h
#ifndef _FBXSDK_SCENE_CONSTRAINT_CHARACTER_NODE_NAME_H_
#define _FBXSDK_SCENE_CONSTRAINT_CHARACTER_NODE_NAME_H_

#include <fbxsdk/fbxsdk_def.h>

#include <fbxsdk/fbxsdk_nsbegin.h>

#define Character_Hips                      "Hips"
#define Character_LeftUpLeg                 "LeftUpLeg"
#define Character_LeftLeg                   "LeftLeg"
#define Character_LeftFoot                  "LeftFoot"
#define Character_RightUpLeg                "RightUpLeg"
#define Character_RightLeg                  "RightLeg"
#define Character_RightFoot                 "RightFoot"
#define Character_Spine                     "Spine"
#define Character_LeftArm                   "LeftArm"
#define Character_LeftForeArm               "LeftForeArm"
#define Character_LeftHand                  "LeftHand"
#define Character_RightArm                  "RightArm"
#define Character_RightForeArm              "RightForeArm"
#define Character_RightHand                 "RightHand"
#define Character_Head                      "Head"
#define Character_LeftToeBase               "LeftToeBase"
#define Character_RightToeBase              "RightToeBase"
#define Character_LeftShoulder              "LeftShoulder"
#define Character_RightShoulder             "RightShoulder"
#define Character_LeftShoulderExtra         "LeftShoulderExtra"
#define Character_RightShoulderExtra        "RightShoulderExtra"
#define Character_Neck                      "Neck"
#define Character_LeftFingerBase            "LeftFingerBase"
#define Character_RightFingerBase           "RightFingerBase"
#define Character_Spine1                    "Spine1"
#define Character_Spine2                    "Spine2"
#define Character_Spine3                    "Spine3"
#define Character_Spine4                    "Spine4"
#define Character_Spine5                    "Spine5"
#define Character_Spine6                    "Spine6"
#define Character_Spine7                    "Spine7"
#define Character_Spine8                    "Spine8"
#define Character_Spine9                    "Spine9"
#define Character_Neck1                     "Neck1"
#define Character_Neck2                     "Neck2"
#define Character_Neck3                     "Neck3"
#define Character_Neck4                     "Neck4"
#define Character_Neck5                     "Neck5"
#define Character_Neck6                     "Neck6"
#define Character_Neck7                     "Neck7"
#define Character_Neck8                     "Neck8"
#define Character_Neck9                     "Neck9"
#define Character_LeftUpLegRoll             "LeftUpLegRoll"
#define Character_LeftLegRoll               "LeftLegRoll"
#define Character_RightUpLegRoll            "RightUpLegRoll"
#define Character_RightLegRoll              "RightLegRoll"
#define Character_LeftArmRoll               "LeftArmRoll"
#define Character_LeftForeArmRoll           "LeftForeArmRoll"
#define Character_RightArmRoll              "RightArmRoll"
#define Character_RightForeArmRoll          "RightForeArmRoll"
#define Character_LeftUpLegRollEx			"LeftUpLegRollEx"
#define Character_LeftLegRollEx             "LeftLegRollEx"
#define Character_RightUpLegRollEx          "RightUpLegRollEx"
#define Character_RightLegRollEx            "RightLegRollEx"
#define Character_LeftArmRollEx				"LeftArmRollEx"
#define Character_LeftForearmRollEx			"LeftForeArmRollEx"
#define Character_RightArmRollEx			"RightArmRollEx"
#define Character_RightForearmRollEx		"RightForeArmRollEx"
#define Character_LeafLeftUpLegRoll1        "LeafLeftUpLegRoll1"
#define Character_LeafLeftLegRoll1          "LeafLeftLegRoll1"
#define Character_LeafRightUpLegRoll1       "LeafRightUpLegRoll1"
#define Character_LeafRightLegRoll1         "LeafRightLegRoll1"
#define Character_LeafLeftArmRoll1          "LeafLeftArmRoll1"
#define Character_LeafLeftForeArmRoll1      "LeafLeftForeArmRoll1"
#define Character_LeafRightArmRoll1         "LeafRightArmRoll1"
#define Character_LeafRightForeArmRoll1     "LeafRightForeArmRoll1"
#define Character_LeafLeftUpLegRoll2        "LeafLeftUpLegRoll2"
#define Character_LeafLeftLegRoll2          "LeafLeftLegRoll2"
#define Character_LeafRightUpLegRoll2       "LeafRightUpLegRoll2"
#define Character_LeafRightLegRoll2         "LeafRightLegRoll2"
#define Character_LeafLeftArmRoll2          "LeafLeftArmRoll2"
#define Character_LeafLeftForeArmRoll2      "LeafLeftForeArmRoll2"
#define Character_LeafRightArmRoll2         "LeafRightArmRoll2"
#define Character_LeafRightForeArmRoll2     "LeafRightForeArmRoll2"
#define Character_LeafLeftUpLegRoll3        "LeafLeftUpLegRoll3"
#define Character_LeafLeftLegRoll3          "LeafLeftLegRoll3"
#define Character_LeafRightUpLegRoll3       "LeafRightUpLegRoll3"
#define Character_LeafRightLegRoll3         "LeafRightLegRoll3"
#define Character_LeafLeftArmRoll3          "LeafLeftArmRoll3"
#define Character_LeafLeftForeArmRoll3      "LeafLeftForeArmRoll3"
#define Character_LeafRightArmRoll3         "LeafRightArmRoll3"
#define Character_LeafRightForeArmRoll3     "LeafRightForeArmRoll3"
#define Character_LeafLeftUpLegRoll4        "LeafLeftUpLegRoll4"
#define Character_LeafLeftLegRoll4          "LeafLeftLegRoll4"
#define Character_LeafRightUpLegRoll4       "LeafRightUpLegRoll4"
#define Character_LeafRightLegRoll4         "LeafRightLegRoll4"
#define Character_LeafLeftArmRoll4          "LeafLeftArmRoll4"
#define Character_LeafLeftForeArmRoll4      "LeafLeftForeArmRoll4"
#define Character_LeafRightArmRoll4         "LeafRightArmRoll4"
#define Character_LeafRightForeArmRoll4     "LeafRightForeArmRoll4"
#define Character_LeafLeftUpLegRoll5        "LeafLeftUpLegRoll5"
#define Character_LeafLeftLegRoll5          "LeafLeftLegRoll5"
#define Character_LeafRightUpLegRoll5       "LeafRightUpLegRoll5"
#define Character_LeafRightLegRoll5         "LeafRightLegRoll5"
#define Character_LeafLeftArmRoll5          "LeafLeftArmRoll5"
#define Character_LeafLeftForeArmRoll5      "LeafLeftForeArmRoll5"
#define Character_LeafRightArmRoll5         "LeafRightArmRoll5"
#define Character_LeafRightForeArmRoll5     "LeafRightForeArmRoll5"
#define Character_Props0                    "Props0"
#define Character_Props1                    "Props1"
#define Character_Props2                    "Props2"
#define Character_Props3                    "Props3"
#define Character_Props4                    "Props4"
#define Character_LeftHandThumb1            "LeftHandThumb1"
#define Character_LeftHandThumb2            "LeftHandThumb2"
#define Character_LeftHandThumb3            "LeftHandThumb3"
#define Character_LeftHandThumb4            "LeftHandThumb4"
#define Character_LeftHandIndex1            "LeftHandIndex1"
#define Character_LeftHandIndex2            "LeftHandIndex2"
#define Character_LeftHandIndex3            "LeftHandIndex3"
#define Character_LeftHandIndex4            "LeftHandIndex4"
#define Character_LeftHandMiddle1           "LeftHandMiddle1"
#define Character_LeftHandMiddle2           "LeftHandMiddle2"
#define Character_LeftHandMiddle3           "LeftHandMiddle3"
#define Character_LeftHandMiddle4           "LeftHandMiddle4"
#define Character_LeftHandRing1             "LeftHandRing1"
#define Character_LeftHandRing2             "LeftHandRing2"
#define Character_LeftHandRing3             "LeftHandRing3"
#define Character_LeftHandRing4             "LeftHandRing4"
#define Character_LeftHandPinky1            "LeftHandPinky1"
#define Character_LeftHandPinky2            "LeftHandPinky2"
#define Character_LeftHandPinky3            "LeftHandPinky3"
#define Character_LeftHandPinky4            "LeftHandPinky4"
#define Character_LeftHandExtraFinger1      "LeftHandExtraFinger1"
#define Character_LeftHandExtraFinger2      "LeftHandExtraFinger2"
#define Character_LeftHandExtraFinger3      "LeftHandExtraFinger3"
#define Character_LeftHandExtraFinger4      "LeftHandExtraFinger4"
#define Character_RightHandThumb1           "RightHandThumb1"
#define Character_RightHandThumb2           "RightHandThumb2"
#define Character_RightHandThumb3           "RightHandThumb3"
#define Character_RightHandThumb4           "RightHandThumb4"
#define Character_RightHandIndex1           "RightHandIndex1"
#define Character_RightHandIndex2           "RightHandIndex2"
#define Character_RightHandIndex3           "RightHandIndex3"
#define Character_RightHandIndex4           "RightHandIndex4"
#define Character_RightHandMiddle1          "RightHandMiddle1"
#define Character_RightHandMiddle2          "RightHandMiddle2"
#define Character_RightHandMiddle3          "RightHandMiddle3"
#define Character_RightHandMiddle4          "RightHandMiddle4"
#define Character_RightHandRing1            "RightHandRing1"
#define Character_RightHandRing2            "RightHandRing2"
#define Character_RightHandRing3            "RightHandRing3"
#define Character_RightHandRing4            "RightHandRing4"
#define Character_RightHandPinky1           "RightHandPinky1"
#define Character_RightHandPinky2           "RightHandPinky2"
#define Character_RightHandPinky3           "RightHandPinky3"
#define Character_RightHandPinky4           "RightHandPinky4"
#define Character_RightHandExtraFinger1     "RightHandExtraFinger1"
#define Character_RightHandExtraFinger2     "RightHandExtraFinger2"
#define Character_RightHandExtraFinger3     "RightHandExtraFinger3"
#define Character_RightHandExtraFinger4     "RightHandExtraFinger4"
#define Character_LeftFootThumb1            "LeftFootThumb1"
#define Character_LeftFootThumb2            "LeftFootThumb2"
#define Character_LeftFootThumb3            "LeftFootThumb3"
#define Character_LeftFootThumb4            "LeftFootThumb4"
#define Character_LeftFootIndex1            "LeftFootIndex1"
#define Character_LeftFootIndex2            "LeftFootIndex2"
#define Character_LeftFootIndex3            "LeftFootIndex3"
#define Character_LeftFootIndex4            "LeftFootIndex4"
#define Character_LeftFootMiddle1           "LeftFootMiddle1"
#define Character_LeftFootMiddle2           "LeftFootMiddle2"
#define Character_LeftFootMiddle3           "LeftFootMiddle3"
#define Character_LeftFootMiddle4           "LeftFootMiddle4"
#define Character_LeftFootRing1             "LeftFootRing1"
#define Character_LeftFootRing2             "LeftFootRing2"
#define Character_LeftFootRing3             "LeftFootRing3"
#define Character_LeftFootRing4             "LeftFootRing4"
#define Character_LeftFootPinky1            "LeftFootPinky1"
#define Character_LeftFootPinky2            "LeftFootPinky2"
#define Character_LeftFootPinky3            "LeftFootPinky3"
#define Character_LeftFootPinky4            "LeftFootPinky4"
#define Character_LeftFootExtraFinger1      "LeftFootExtraFinger1"
#define Character_LeftFootExtraFinger2      "LeftFootExtraFinger2"
#define Character_LeftFootExtraFinger3      "LeftFootExtraFinger3"
#define Character_LeftFootExtraFinger4      "LeftFootExtraFinger4"
#define Character_RightFootThumb1           "RightFootThumb1"
#define Character_RightFootThumb2           "RightFootThumb2"
#define Character_RightFootThumb3           "RightFootThumb3"
#define Character_RightFootThumb4           "RightFootThumb4"
#define Character_RightFootIndex1           "RightFootIndex1"
#define Character_RightFootIndex2           "RightFootIndex2"
#define Character_RightFootIndex3           "RightFootIndex3"
#define Character_RightFootIndex4           "RightFootIndex4"
#define Character_RightFootMiddle1          "RightFootMiddle1"
#define Character_RightFootMiddle2          "RightFootMiddle2"
#define Character_RightFootMiddle3          "RightFootMiddle3"
#define Character_RightFootMiddle4          "RightFootMiddle4"
#define Character_RightFootRing1            "RightFootRing1"
#define Character_RightFootRing2            "RightFootRing2"
#define Character_RightFootRing3            "RightFootRing3"
#define Character_RightFootRing4            "RightFootRing4"
#define Character_RightFootPinky1           "RightFootPinky1"
#define Character_RightFootPinky2           "RightFootPinky2"
#define Character_RightFootPinky3           "RightFootPinky3"
#define Character_RightFootPinky4           "RightFootPinky4"
#define Character_RightFootExtraFinger1     "RightFootExtraFinger1"
#define Character_RightFootExtraFinger2     "RightFootExtraFinger2"
#define Character_RightFootExtraFinger3     "RightFootExtraFinger3"
#define Character_RightFootExtraFinger4     "RightFootExtraFinger4"
#define Character_LeftInHandThumb           "LeftInHandThumb"
#define Character_LeftInHandIndex           "LeftInHandIndex"
#define Character_LeftInHandMiddle          "LeftInHandMiddle"
#define Character_LeftInHandRing            "LeftInHandRing"
#define Character_LeftInHandPinky           "LeftInHandPinky"
#define Character_LeftInHandExtraFinger     "LeftInHandExtraFinger"
#define Character_RightInHandThumb          "RightInHandThumb"
#define Character_RightInHandIndex          "RightInHandIndex"
#define Character_RightInHandMiddle         "RightInHandMiddle"
#define Character_RightInHandRing           "RightInHandRing"
#define Character_RightInHandPinky          "RightInHandPinky"
#define Character_RightInHandExtraFinger    "RightInHandExtraFinger"
#define Character_LeftInFootThumb           "LeftInFootThumb"
#define Character_LeftInFootIndex           "LeftInFootIndex"
#define Character_LeftInFootMiddle          "LeftInFootMiddle"
#define Character_LeftInFootRing            "LeftInFootRing"
#define Character_LeftInFootPinky           "LeftInFootPinky"
#define Character_LeftInFootExtraFinger     "LeftInFootExtraFinger"
#define Character_RightInFootThumb          "RightInFootThumb"
#define Character_RightInFootIndex          "RightInFootIndex"
#define Character_RightInFootMiddle         "RightInFootMiddle"
#define Character_RightInFootRing           "RightInFootRing"
#define Character_RightInFootPinky          "RightInFootPinky"
#define Character_RightInFootExtraFinger    "RightInFootExtraFinger"

#define Character_GameModeParentLeftHipRoll			"GameModeParentLeftHipRoll"
#define Character_GameModeParentLeftKnee			"GameModeParentLeftKnee"
#define Character_GameModeParentLeftKneeRoll		"GameModeParentLeftKneeRoll"
#define Character_GameModeParentRightHipRoll		"GameModeParentRightHipRoll"
#define Character_GameModeParentRightKnee			"GameModeParentRightKnee"
#define Character_GameModeParentRightKneeRoll		"GameModeParentRightKneeRoll"
#define Character_GameModeParentLeftShoulderRoll	"GameModeParentLeftShoulderRoll"
#define Character_GameModeParentLeftElbow			"GameModeParentLeftElbow"
#define Character_GameModeParentLeftElbowRoll		"GameModeParentLeftElbowRoll"
#define Character_GameModeParentRightShoulderRoll	"GameModeParentRightShoulderRoll"
#define Character_GameModeParentRightElbow			"GameModeParentRightElbow"
#define Character_GameModeParentRightElbowRoll		"GameModeParentRightElbowRoll"

#define Character_LeftFloorContact                  "LeftFloorContact"
#define Character_RightFloorContact                 "RightFloorContact"
#define Character_LeftHandFloorContact              "LeftHandFloorContact"
#define Character_RightHandFloorContact             "RightHandFloorContact"

#include <fbxsdk/fbxsdk_nsend.h>

#endif /* _FBXSDK_SCENE_CONSTRAINT_CHARACTER_NODE_NAME_H_ */
