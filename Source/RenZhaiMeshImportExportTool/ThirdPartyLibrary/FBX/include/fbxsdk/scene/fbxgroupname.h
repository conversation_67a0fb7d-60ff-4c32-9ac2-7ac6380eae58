/****************************************************************************************
 
   Copyright (C) 2015 Autodesk, Inc.
   All rights reserved.
 
   Use of this software is subject to the terms of the Autodesk license agreement
   provided at the time of installation or download, or which otherwise accompanies
   this software in either electronic or hard copy form.
 
****************************************************************************************/

//! \file fbxgroupname.h
#ifndef _FBXSDK_SCENE_GROUP_NAME_H_
#define _FBXSDK_SCENE_GROUP_NAME_H_

#include <fbxsdk/fbxsdk_def.h>

#include <fbxsdk/fbxsdk_nsbegin.h>

#define MODEL_PREFIX					"Model::"
#define MATERIAL_PREFIX					"Material::"
#define POSE_PREFIX						"Pose::"
#define DEFORMER_PREFIX					"Deformer::"
#define CONTAINER_PREFIX				"Container::"
#define SUBDEFORMER_PREFIX				"SubDeformer::"
#define CONSTRAINT_PREFIX				"Constraint::"
#define CONTROLSET_PLUG_PREFIX			"ControlSetPlug::"
#define AUDIO_PREFIX					"Audio::"
#define AUDIO_LAYER_PREFIX				"AudioLayer::"
#define VIDEO_PREFIX					"Video::"
#define TEXTURE_PREFIX					"Texture::"
#define THUMBNAIL_PREFIX				"Thumbnail::"
#define MARKER_SET_PREFIX				"MarkerSet::"
#define CONSTRAINT_PREFIX				"Constraint::"
#define GEOMETRY_PREFIX					"Geometry::"
#define GEOMETRY_WEIGHTED_MAP_PREFIX	"GeometryWeightedMap::"
#define SCENE_PREFIX					"Scene::"
#define CACHE_PREFIX					"Cache::"
#define IMPLEMENTATION_PREFIX			"Implementation::"
#define BINDINGTABLE_PREFIX				"BindingTable::"
#define BINDINGOPERATOR_PREFIX			"BindingOperator::"
#define LAYERED_TEXTURE_PREFIX			"LayeredTexture::"
#define PROCEDURAL_TEXTURE_PREFIX		"ProceduralTexture::"
#define DOCUMENT_PREFIX					"Document::"
#define COLLECTION_PREFIX				"Collection::"
#define COLLECTION_EXCLUSIVE_PREFIX		"CollectionExclusive::"
#define NODE_ATTRIBUTE_PREFIX			"NodeAttribute::"
#define ENVIRONMENT_PREFIX				"KFbxEnvironment::"
#define OBJECTMETADATA_PREFIX			"ObjectMetaData::"
#define EXPOSURECONTROL_PREFIX			"ExposureControl::"

#define ANIM_STACK_PREFIX				"AnimStack::"
#define ANIM_LAYER_PREFIX				"AnimLayer::"
#define ANIM_CURVENODE_PREFIX			"AnimCurveNode::"
#define ANIM_CURVE_PREFIX				"AnimCurve::"
#define ANIM_EVAL_PREFIX				"AnimEvaluator::"

#define SELECTION_SET_PREFIX			"SelectionSet::"
#define SELECTION_SET_NODE_PREFIX		"SelectionNode::"
#define DISPLAY_LAYER_PREFIX			"DisplayLayer::"

#define SCENEREFERENCE_PREFIX			"SceneReference::"

// Protein 2.0
#define ADSK_ENVIRONMENT_PREFIX			"ADSKEnvironmentDefinition::"
#define ADSK_LIGHT_PREFIX				"ADSKLightDefinition::"
#define ADSK_PROCEDURALGEOMETRY_PREFIX	"ADSKProceduralGeometryDefinition::"
#define ADSK_SURFACEMATERIAL_PREFIX		"ADSKSurfaceMaterialDefinition::"
#define ADSK_TEXTURE_PREFIX				"ADSKTextureDefinition::"
#define ADSK_SWATCHSCENE_PREFIX			"ADSKSwatchSceneDefinition::"

#include <fbxsdk/fbxsdk_nsend.h>

#endif /* _FBXSDK_SCENE_GROUP_NAME_H_ */
